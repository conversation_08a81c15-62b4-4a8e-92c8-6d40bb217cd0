APP_NAME=VIETEC_QLDN
APP_ENV=staging
APP_KEY=base64:t1I/iW/rFr0wZdgAW6Ydxy5eshwq4WKLDlGx2ZN5af8=
APP_DEBUG=true
APP_URL=http://dev.internal.vietec.com.vn

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=************
DB_PORT=3306
DB_DATABASE=vietec_qldn_dev
DB_USERNAME=vietec_qldn
DB_PASSWORD=k4YU7uhsshVrKGvVwJBU2wDVXUYx53

CALLCENTER_DB_CONNECTION=mysql
CALLCENTER_DB_HOST=data2.vietec.com.vn
CALLCENTER_DB_PORT=3306
CALLCENTER_DB_DATABASE=smartWeb_2_0
CALLCENTER_DB_USERNAME=sondp
CALLCENTER_DB_PASSWORD=123456

BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.yandex.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=4WPmjvNtbnP7EwmXK9dq8XZYF
MAIL_ENCRYPTION=ssl
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=*******
PUSHER_APP_KEY=db9731b19ef0753b890a
PUSHER_APP_SECRET=d86ad28872f4e38df709
PUSHER_APP_CLUSTER=ap1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MIX_APP_URL="${APP_URL}"
JWT_SECRET=viKu7MMzdfuESx0ayussjsRnx8Je6zbL8jCzxEV03bY8lNHrl3yMD6mLCwwlwLtW
JWT_TTL=1440
JWT_REFRESH_TTL=20160

HANET_API_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************.yb6ZnCCS9HBwaYDea4AuYM2jGBEtjjeJlxXIaDLPOMc
HANET_API_ADDRESS=https://partner.hanet.ai
HANET_API_PLACEID=10818
HANET_API_PLACEID_HCM=27568

ONESIGNAL_APP_ID=************************************
ONESIGNAL_REST_API_KEY=************************************************
USER_AUTH_KEY=ZDNiMmM1ODItNTNkNy00NWFhLWJiZTUtOTAzZjdjNTdjMjY3
MIX_ONESIGNAL_APP_ID="${ONESIGNAL_APP_ID}"

SUPPER_PASS=123456

# firebase admin config account
FIREBASE_SERVICE_ACCOUNT_KEY_PATH=config/stg-qldn-firebase-adminsdk-u2zli-e51775c32c.json
FIREBASE_CREDENTIALS=config/stg-qldn-firebase-adminsdk-u2zli-e51775c32c.json

QUEUE_NAME_PREFIX=qldn_stg

# AMQP
AMQP_CONNECTION=rabbitmq
AMQP_HOST_VIETEC=rabbit.pcgd.vn
AMQP_PORT_VIETEC=5672
AMQP_USER_VIETEC=vietec
AMQP_PASSWORD_VIETEC=zuGjQQEcLwSC3cXCS6pmr3L4FNjuuJ
AMQP_VHOST_VIETEC=/vietec
AMQP_EXCHANGE_NAME_VIETEC=vietec_staging.direct
AMQP_QUEUE_NAME_VIETEC=vietec_staging_notifications
AMQP_QUEUE_NAME_PREFIX_VIETEC=vietec_staging_