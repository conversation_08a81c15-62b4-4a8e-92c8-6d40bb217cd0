# Hướng Dẫn Sử Dụng Module PartnerManagement

## Tổng Quan

Module PartnerManagement là một hệ thống quản lý đối tác tích hợp với hệ thống PMS (Project Management System), cho ph<PERSON><PERSON> quản lý đối tác, công ty đối tác, g<PERSON><PERSON><PERSON> phé<PERSON> sử dụng và tạo tài khoản PMS tự động.

## Cấu Trúc Module

### Models Chính

1. **Partner** - Quản lý thông tin đối tác
2. **PartnerCompany** - Quản lý công ty đối tác  
3. **PartnerLicense** - Quản lý giấy phép sử dụng
4. **PMS Models** - Kết nối với database PMS

### Controllers

1. **AuthController** - <PERSON><PERSON><PERSON> thực đối tác
2. **PartnerController** - <PERSON><PERSON><PERSON>n lý đối tác
3. **PartnerLicenseController** - Quản lý gi<PERSON>y phép
4. **AccountController** - <PERSON><PERSON><PERSON> tà<PERSON> PMS
5. **PartnerNotificationController** - Thông báo

## Cài Đặt và Cấu Hình

### 1. Cấu Hình Database

Thêm kết nối database PMS vào `config/database.php`:

```php
'mysql_pms' => [
    'driver' => 'mysql',
    'host' => env('DB_PMS_HOST', '127.0.0.1'),
    'port' => env('DB_PMS_PORT', '3306'),
    'database' => env('DB_PMS_DATABASE', 'pms'),
    'username' => env('DB_PMS_USERNAME', 'root'),
    'password' => env('DB_PMS_PASSWORD', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
],
```

### 2. Chạy Migration

```bash
php artisan migrate
```

### 3. Cấu Hình Pusher (Thông báo realtime)

Thêm vào `.env`:
```
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

## Sử Dụng Commands

### 1. Tạo Công Ty Đối Tác

```bash
php artisan partner:create-company --name="Công ty ABC" --short_name="ABC" --prefix_code="ABC" --status=1
```

**Tham số:**
- `--name`: Tên công ty (bắt buộc)
- `--short_name`: Tên viết tắt
- `--prefix_code`: Mã tiền tố (tối đa 100 ký tự)
- `--status`: Trạng thái (1: hoạt động, 0: không hoạt động)

### 2. Tạo Giấy Phép

```bash
php artisan partner:generate-licenses 10 3
```

**Tham số:**
- `count`: Số lượng giấy phép cần tạo
- `month`: Thời hạn tính theo tháng

### 3. Gán Giấy Phép Cho Công Ty

```bash
php artisan partner:assign-licenses --company_id=1 --count=5 --month=3
```

**Tham số:**
- `--company_id`: ID công ty
- `--count`: Số lượng giấy phép cần gán
- `--month`: Thời hạn tính theo tháng

### 4. Tạo Tài Khoản PMS

```bash
php artisan pms:create-account --province=123 --district=456 --ward=789 --school_name="Trường ABC" --username="truongabc" --school_level=1 --branches=1 --address="123 Đường ABC" --phone="**********" --email="<EMAIL>" --projects=1,2 --duration=12
```

**Tham số:**
- `--province`: ID Sở giáo dục (PMSUnit level=2)
- `--district`: ID Phòng giáo dục (PMSUnit level=3)  
- `--ward`: ID Phường/xã
- `--school_name`: Tên trường
- `--username`: Tên đăng nhập
- `--school_level`: Cấp học
- `--branches`: Số điểm trường
- `--address`: Địa chỉ
- `--phone`: Số điện thoại
- `--email`: Email
- `--projects`: Danh sách ID dự án (phân cách bằng dấu phẩy)
- `--duration`: Thời hạn (tháng)

### 5. Cập Nhật Giấy Phép Hết Hạn

```bash
php artisan partner:update-expired-licenses
```

Lệnh này sẽ tự động cập nhật trạng thái giấy phép hết hạn thành `STATUS_EXPIRED`.

## API Endpoints

### Authentication

#### Đăng Nhập
```
POST /api/partners/auth/login
```

**Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password"
}
```

#### Đăng Ký
```
POST /api/partners/auth/register
```

**Body:**
```json
{
    "name": "Tên đối tác",
    "code": "PARTNER001",
    "email": "<EMAIL>",
    "password": "password",
    "phone": "**********",
    "address": "Địa chỉ",
    "company_id": 1
}
```

#### Đăng Xuất
```
POST /api/partners/auth/logout
Authorization: Bearer {token}
```

#### Làm Mới Token
```
POST /api/partners/auth/refresh
Authorization: Bearer {token}
```

#### Thông Tin Profile
```
GET /api/partners/auth/profile
Authorization: Bearer {token}
```

### Quản Lý Giấy Phép

#### Danh Sách Giấy Phép
```
GET /api/partners/license/list
Authorization: Bearer {token}
```

**Query Parameters:**
- `product_id`: Lọc theo ID sản phẩm
- `status`: Lọc theo trạng thái
- `project_account`: Tìm kiếm theo tài khoản dự án
- `expired_in`: Lọc giấy phép hết hạn trong X ngày

#### Cập Nhật Giấy Phép
```
POST /api/partners/license/update/{id}
Authorization: Bearer {token}
```

#### Khóa Giấy Phép
```
POST /api/partners/license/lock/{id}
Authorization: Bearer {token}
```

#### Mở Khóa Giấy Phép
```
POST /api/partners/license/unlock/{id}
Authorization: Bearer {token}
```

#### Gia Hạn Giấy Phép
```
POST /api/partners/license/renew/{id}
Authorization: Bearer {token}
```

**Body:**
```json
{
    "month": 12
}
```

### Tạo Tài Khoản PMS

#### Tạo Tài Khoản QLMN
```
POST /api/partners/account/qlmn/add
Authorization: Bearer {token}
```

**Body:**
```json
{
    "province_id": 123,
    "district_id": 456,
    "ward_id": 789,
    "school_name": "Trường ABC",
    "account": "truongabc",
    "school_level": 1,
    "school_point": 1,
    "address": "123 Đường ABC",
    "phone": "**********",
    "email": "<EMAIL>",
    "product_id": 1,
    "expired_time": 12,
    "from_date": "2024-01-01",
    "to_date": "2024-12-31"
}
```

### Thông Báo

#### Danh Sách Thông Báo
```
GET /api/partners/notifications
Authorization: Bearer {token}
```

#### Thông Báo Chưa Đọc
```
GET /api/partners/notifications/unread
Authorization: Bearer {token}
```

#### Đánh Dấu Đã Đọc
```
POST /api/partners/notifications/{id}/read
Authorization: Bearer {token}
```

#### Đánh Dấu Tất Cả Đã Đọc
```
POST /api/partners/notifications/read-all
Authorization: Bearer {token}
```

## Trạng Thái Giấy Phép

- `0` (STATUS_CREATED): Mới tạo
- `1` (STATUS_ASSIGNED): Đã gán cho đối tác
- `2` (STATUS_ACTIVATED): Đã kích hoạt
- `3` (STATUS_EXPIRED): Hết hạn
- `4` (STATUS_PENDING): Tạm khóa

## Quy Trình Sử Dụng

### 1. Tạo Công Ty và Đối Tác
1. Tạo công ty đối tác bằng command
2. Đăng ký tài khoản đối tác qua API
3. Gán đối tác vào công ty

### 2. Quản Lý Giấy Phép
1. Tạo giấy phép bằng command
2. Gán giấy phép cho công ty
3. Đối tác sử dụng giấy phép để tạo tài khoản PMS

### 3. Tạo Tài Khoản PMS
1. Đối tác đăng nhập vào hệ thống
2. Sử dụng API tạo tài khoản PMS
3. Hệ thống tự động gán giấy phép và tạo tài khoản

### 4. Theo Dõi và Quản Lý
1. Theo dõi trạng thái giấy phép
2. Gia hạn giấy phép khi cần thiết
3. Nhận thông báo realtime qua Pusher

## Lưu Ý Quan Trọng

1. **Bảo Mật**: Tất cả API đều yêu cầu xác thực JWT
2. **Quyền Hạn**: Sử dụng Spatie Laravel Permission để quản lý quyền
3. **Cache**: Hệ thống sử dụng cache để tối ưu hiệu suất
4. **Transaction**: Các thao tác quan trọng đều sử dụng database transaction
5. **Notification**: Hỗ trợ thông báo realtime qua Pusher
6. **Logging**: Tất cả hoạt động đều được ghi log để audit

## Troubleshooting

### Lỗi Kết Nối Database PMS
- Kiểm tra cấu hình database trong `.env`
- Đảm bảo database PMS đã được tạo và có dữ liệu

### Lỗi Pusher
- Kiểm tra cấu hình Pusher trong `.env`
- Đảm bảo credentials Pusher chính xác

### Lỗi Permission
- Kiểm tra roles và permissions đã được tạo
- Đảm bảo user đã được gán role phù hợp

### Lỗi Cache
- Xóa cache: `php artisan cache:clear`
- Restart queue worker nếu sử dụng queue
