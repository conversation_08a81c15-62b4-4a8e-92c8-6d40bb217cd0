before_script:
  - eval $(ssh-agent -s)
  - ssh-add <(echo "$PRIVATE_KEY")
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'

cache:
  paths:
    - node_modules/

stages:
  # - build
  # - test
  - staging
  - master

#build:
#  image: node:14
#  stage: build
#  only:
#    - staging
#  script:
#    - npm install
#    - npm run build

# test:
#   stage: test
#   script:
#     - dotnet test

staging:
  image: tuan1st/dotnetcore22
  stage: staging
  only:
    - staging
  script:
    - echo "'Staging - Commit = $CI_COMMIT_SHORT_SHA'" > ./public/version.txt
    - rm -rf ./.env
    - mv ./.env.staging ./.env
    - rsync -avIz -e "ssh -p $SSH_PORT" --exclude-from="deploy_exclude.txt" ./ $ACCOUNT@$SERVER:$DEPLOY_DIR/staging_public_html
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "cd $DEPLOY_DIR/staging_public_html && npm install && npm run production && composer install"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chown -R $ACCOUNT:$ACCOUNT $DEPLOY_DIR/staging_public_html"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chmod -R 755 $DEPLOY_DIR/staging_public_html"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chmod -R 777 $DEPLOY_DIR/staging_public_html/storage"

master:
  image: tuan1st/dotnetcore22
  stage: master
  only:
    - master
  script:
    - echo "'Master - Commit = $CI_COMMIT_SHORT_SHA'" > ./public/version.txt
    - rm -rf ./.env
    - mv ./.env.master ./.env
    - rsync -avIz -e "ssh -p $SSH_PORT" --exclude-from="deploy_exclude.txt" ./ $ACCOUNT@$SERVER:$DEPLOY_DIR/public_html
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "cd $DEPLOY_DIR/public_html && php8.2 /usr/local/bin/composer install && yarn install && yarn run production"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chown -R $ACCOUNT:$ACCOUNT $DEPLOY_DIR/"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chmod -R 755 $DEPLOY_DIR/public_html"
    - ssh $ACCOUNT@$SERVER -p $SSH_PORT "chmod -R 777 $DEPLOY_DIR/public_html/storage"
