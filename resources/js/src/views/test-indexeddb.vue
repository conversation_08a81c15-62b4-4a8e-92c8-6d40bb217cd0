<template>
    <div class="layout-px-spacing">
        <portal to="breadcrumb">
            <div class="page-title">
                <h3>Test IndexedDB</h3>
            </div>
            <div class="breadcrumb-four">
                <ul class="breadcrumb">
                    <li class="active">
                        <router-link to="/">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="feather feather-home"
                            >
                                <path
                                    d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                                ></path>
                                <polyline
                                    points="9 22 9 12 15 12 15 22"
                                ></polyline>
                            </svg>
                        </router-link>
                    </li>
                    <li class="active">
                        <a href="javascript:void(0);">Test IndexedDB</a>
                    </li>
                </ul>
            </div>
        </portal>

        <div class="row layout-top-spacing">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing">
                <div class="widget widget-card-four">
                    <div class="widget-content">
                        <div class="w-content">
                            <h3>Test IndexedDB Storage</h3>
                            <div class="form-group">
                                <label>Key:</label>
                                <input v-model="key" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label>Value:</label>
                                <textarea v-model="value" class="form-control" rows="5"></textarea>
                                <small class="form-text text-muted">
                                    You can enter a simple string or a JSON object (e.g., {"name": "John", "age": 30})
                                </small>
                            </div>
                            <div class="form-group">
                                <button @click="saveData" class="btn btn-primary mr-2">Save</button>
                                <button @click="getData" class="btn btn-info mr-2">Get</button>
                                <button @click="removeData" class="btn btn-danger mr-2">Remove</button>
                                <button @click="clearAll" class="btn btn-warning">Clear All</button>
                            </div>
                            <div v-if="result" class="mt-4">
                                <h4>Result:</h4>
                                <pre>{{ result }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import storage from "@/helpers/storage";

export default {
    data() {
        return {
            key: "test-key",
            value: "test-value",
            result: null
        };
    },
    methods: {
        async saveData() {
            try {
                let valueToStore = this.value;
                let displayValue = this.value;

                // Try to parse as JSON if it looks like an object or array
                if (this.value.trim().startsWith('{') || this.value.trim().startsWith('[')) {
                    try {
                        valueToStore = JSON.parse(this.value);
                        displayValue = JSON.stringify(valueToStore, null, 2);
                    } catch (e) {
                        // Not valid JSON, store as string
                        this.result = `Warning: Value looks like JSON but couldn't be parsed. Storing as string.`;
                    }
                }

                // Use setObject for objects to ensure proper storage
                if (typeof valueToStore === 'object' && valueToStore !== null) {
                    await storage.setObject(this.key, valueToStore);
                } else {
                    await storage.setItem(this.key, valueToStore);
                }

                this.result = `Data saved successfully:\nKey: ${this.key}\nValue: ${displayValue}`;
            } catch (error) {
                this.result = `Error saving data: ${error.message}`;
            }
        },
        async getData() {
            try {
                // Try getObject first to handle JSON data properly
                const data = await storage.getObject(this.key);

                if (data === null || data === undefined) {
                    this.result = `No data found for key: ${this.key}`;
                    return;
                }

                if (typeof data === 'object' && data !== null) {
                    this.result = JSON.stringify(data, null, 2);
                } else {
                    this.result = data;
                }
            } catch (error) {
                this.result = `Error getting data: ${error.message}`;
            }
        },
        async removeData() {
            try {
                await storage.removeItem(this.key);
                this.result = `Data removed successfully: ${this.key}`;
            } catch (error) {
                this.result = `Error removing data: ${error.message}`;
            }
        },
        async clearAll() {
            try {
                await storage.clear();
                this.result = "All data cleared successfully";
            } catch (error) {
                this.result = `Error clearing data: ${error.message}`;
            }
        }
    }
};
</script>
