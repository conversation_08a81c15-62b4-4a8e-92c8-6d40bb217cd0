<template>
  <div class="layout-px-spacing">
    <breadcrumb :lists="breadcrumb_option"></breadcrumb>
    <div class="row layout-top-spacing">
      <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
        <b-overlay :show="html.overlay_show" rounded="top">
          <div class="panel br-6 p-0">
            <div style="display: flex;">
                <div class="d-flex flex-wrap justify-content-center justify-content-sm-start px-3 pt-3 pb-0">
                    <b-button class="ml-1 mb-1 btn-sm" to="/contract/add" variant="primary" v-permission="['contract_add']">
                        <i data-feather="plus" class="size-14"></i>
                        Thêm mới
                    </b-button>
                    <b-button class="ml-1 mb-1 btn-sm" to="/contract/payment" variant="primary"
                              v-permission="['contract_add']">
                        <i data-feather="plus" class="size-14"></i>
                        Thêm thanh toán
                    </b-button>
                    <b-button to="/contract/import" variant="secondary" class="ml-1 mb-1 btn-sm"
                              v-permission="['import_excel_contract']">
                        <i data-feather="arrow-up-circle" class="size-14"></i>
                        Cập nhật hợp đồng
                    </b-button>
                    <b-button variant="success" class="ml-1 mb-1 btn-sm" @click="download_file"
                              v-permission="['contract_add']"><i data-feather="arrow-down-circle" class="size-14"></i>
                        Tải file mẫu
                    </b-button>
                    <b-button variant="info" class="ml-1 mb-1 btn-sm" @click="download_report"
                              v-permission="['download_report_excel']"><i data-feather="arrow-down-circle" class="size-14"></i>
                        Xuất hóa đơn
                    </b-button>
                    <b-button variant="warning" class="ml-1 mb-1 btn-sm" to="/contract/review-list"
                              v-permission="['show_import_contract_list']">
                        Duyệt hợp đồng
                    </b-button>
                    <b-button variant="primary" class="ml-1 mb-1 btn-sm" to="/contract/review-contract-form"
                              v-permission="['review_contract', 'contract_add']">
                        HĐ nhập trực tiếp
                    </b-button>
                </div>
                <div class="header-search">
                    <a href="javascript:" v-b-tooltip title="Tìm kiếm" v-b-toggle="'collapse-search'">
                        <i data-feather="search" class="size-18"></i>
                    </a>
                </div>
            </div>
            <b-collapse id="collapse-search" class="collapse-search" visible>
              <b-card class="card-body">
                <b-form-row>
                  <b-form-group label="Tỉnh/thành phố" class="col mr-b-5">
                    <b-form-select v-model="filter.province_id" :options="provinces" value-field="province_id"
                      text-field="name" @input="changeProvince" size="sm"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Quận/huyện" class="col mr-b-5">
                    <b-form-select v-model="filter.district_id" :options="districts" value-field="district_id"
                      text-field="name" size="sm"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Tên hệ thống" class="col mr-b-5">
                    <b-form-select v-model="filter.product_id" :options="products" value-field="id" size="sm"
                      text-field="code"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Team" class="col mr-b-5">
                    <b-form-select v-model="filter.team" :options="teams" value-field="representatives" size="sm"
                      text-field="name"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Công ty" class="col mr-b-5">
                    <b-form-select v-model="filter.company" :options="companies" value-field="id" size="sm"
                      text-field="name"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Yêu cầu XHĐ" class="col mr-b-5">
                    <b-form-select v-model="filter.invoice_request" :options="invoice_requests" value-field="id" size="sm"
                      text-field="name"></b-form-select>
                  </b-form-group>
                </b-form-row>
                <b-form-row>
                  <b-form-group label="Trạng thái XHĐ" class="col mr-b-5">
                    <b-form-select v-model="filter.invoice_status" :options="invoice_status" value-field="id" size="sm"
                      text-field="name"></b-form-select>
                  </b-form-group>
                  <b-form-group label="Tháng tài chính" class="col">
                    <date-picker v-model="filter.month" type="month" format="MM" value-type="format" style="height: 39px;"></date-picker>
                  </b-form-group>
                  <b-form-group label="Năm tài chính" class="col">
                    <date-picker v-model="filter.year" type="year" format="YYYY" value-type="format" style="height: 39px;"></date-picker>
                  </b-form-group>
                  <b-form-group label="Ngày tạo hợp đồng" class="col mr-b-5">
                    <flat-pickr class="form-control flatpickr active" v-model="filter.created_at" style="height: 39px;">
                    </flat-pickr>
                  </b-form-group>
                  <b-form-group label="Mã khách hàng" class="col mr-b-5">
                    <b-input type="text" v-model="filter.client_code" size="sm"></b-input>
                  </b-form-group>
                  <b-form-group label="Tên trường" class="col mr-b-5">
                    <b-input type="text" v-model="filter.school_name" size="sm"></b-input>
                  </b-form-group>
                </b-form-row>
                <div class="group-button">
                  <b-button class="button-reset default-color btn-sm" @click="reset">Làm mới
                  </b-button>
                  <b-button class="btn-sm" variant="info" @click="change_filter">Tìm kiếm
                  </b-button>
                </div>
              </b-card>
            </b-collapse>
            <div class="custom-table">
              <b-table ref="contract_table" responsive hover small class="bordered-custome contract" :items="items"
                :fields="columns">
                <template #head(id)>
                  <b-checkbox v-role="['accounting', 'accounting_leader']" v-permission="['download_report_excel']"
                    v-model="is_select_all" variant="primary" class="checkbox-outline-primary" @change="select_all()" />
                  <span v-role="['sale', 'sale_director', 'sale_admin', 'sale_member']"
                    v-permission="['download_report_excel']">
                    STT
                  </span>
                </template>
                <template #cell(id)="row">
                  <b-checkbox v-role="['accounting', 'accounting_leader']" v-permission="['download_report_excel']"
                    v-model="selected_rows" :value="row.value" variant="primary" class="checkbox-outline-primary"
                    @change="check_select_all">
                  </b-checkbox>
                  <span v-role="['sale', 'sale_director', 'sale_admin', 'sale_member']"
                    v-permission="['download_report_excel']">
                    {{ row.index + 1 }}
                  </span>
                </template>
                <template #cell(contract_value)="row">
                  {{ row.value | formatMoney }}
                </template>
                <template #cell(payment_amount)="row">
                  {{ row.value | formatMoney }}
                </template>
                <template #cell(received_money)="row">
                  {{ row.value | formatMoney }}
                </template>
                <template #cell(debt)="row">
                  {{ row.value | formatMoney }}
                </template>
                <template #cell(sale_date)="row">
                  {{ row.value | formatDate2 }}
                </template>
                <template #cell(invoice_request)="row">
                  {{ row.value == 1 ? "Có" : "Không" }}
                </template>
                <template #cell(invoice_status)="row">
                  {{ row.value == 1 ? "Đã xuất" : "Chưa xuất" }}
                </template>
                <template #cell(action)="row">
                  <a :href="'/contract/details/' + row.item.id" class="cancel" v-permission="[
                    'contract_detail',
                    'contract_list_management',
                  ]" title="Chi tiết">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="feather feather-eye yellow" data-v-8d2239c6="">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                  </a>
                  <a 
                    class="cancel" 
                    :href="'/contract/edit/' + row.item.id" 
                    title="Chỉnh sửa"
                    v-if="checkPermission(['contract_edit']) && checkPerEdit(row.item)"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="feather feather-edit-3 text-primary">
                      <path d="M12 20h9"></path>
                      <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                    </svg>
                  </a>
                  <a 
                    href="javascript:;" 
                    class="cancel" 
                    @click="delete_row(row.item)" 
                    v-if="checkPermission(['contract_delete']) && checkPerDelete(row.item)"
                    title="Xoá"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="feather feather-trash-2 text-danger">
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                      <line x1="10" y1="11" x2="10" y2="17"></line>
                      <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                  </a>
                </template>
              </b-table>
              <pagination :table_option="table_option" :routing="change_page"></pagination>
              <div class="row" style="padding: 0px 55px 0px 30px;">
                  <div class="col-md-4">
                      <h6 class="fw">
                          Tổng giá trị hợp đồng:
                          {{ sum_contract_value | formatMoney }}
                      </h6>
                      <h6 class="fw">
                          Tổng đã thu:
                          {{ sum_received_money | formatMoney }}
                      </h6>
                      <h6 class="fw">Tổng công nợ: {{ sum_debt | formatMoney }}</h6>
                  </div>
                  <div class="col-md-5"></div>
                  <div class="d-flex align-items-center col-3" style="justify-content: right;">
                      <span>Cài đặt hiển thị :</span>
                      <span class="ml-2">
                          <b-select v-model="table_option.per_page" size="sm">
                            <b-select-option value="5">5</b-select-option>
                            <b-select-option value="10">10</b-select-option>
                            <b-select-option value="20">20</b-select-option>
                            <b-select-option value="50">50</b-select-option>
                          </b-select>
                      </span>
                  </div>
              </div>
            </div>
          </div>
        </b-overlay>
      </div>
    </div>
  </div>
</template>

<script>
  import c from "@/helpers/common";
  import "@/views/style.css";
  import feather from "feather-icons";
  import flatPickr from "vue-flatpickr-component";
  import DatePicker from "vue2-datepicker";
  import "vue2-datepicker/index.css";
  import "flatpickr/dist/flatpickr.css";
  import "@/assets/sass/forms/custom-flatpickr.css";
  import permission from "@/directive/permission/index.js";
  import role from "@/directive/role/index.js";
  import checkPermission from "@/helpers/permission";
  import breadcrumb from "@/views/components/breadcrumb";
  import pagination from "@/views/components/pagination";
  import moment from "moment";
  import saveAs from "file-saver";

  export default {
    directives: { permission, role },
    mounted() {
      feather.replace();
    },
    components: {
      flatPickr,
      breadcrumb,
      pagination,
      saveAs,
      DatePicker,
    },
    data() {
      return {
        breadcrumb_option: ["Quản lý khách hàng", "Danh sách hợp đồng"],
        columns: [],
        items: [],
        table_option: {
          total: "",
          current_page: 1,
          per_page: "",
          from: "",
          to: "",
        },
        html: {
          overlay_show: false,
        },
        sum_contract_value: "",
        sum_received_money: "",
        sum_debt: "",
        filter: {
          province_id: "",
          district_id: "",
          product_id: "",
          school_name: "",
          client_code: "",
          team: "",
          company: "",
          invoice_request: "",
          invoice_status: "",
          created_at: "",
          month: "",
          year: moment().format("YYYY"),
        },
        products: [{ id: "", code: "Chọn" }],
        provinces: [{ province_id: "", name: "Chọn" }],
        districts: [{ district_id: "", name: "Chọn" }],
        teams: [{ representatives: "", name: "Chọn" }],
        companies: [{ id: "", name: "Chọn" }],
        invoice_requests: [{ id: "", name: "Chọn" }],
        invoice_status: [{ id: "", name: "Chọn" }],
        is_select_all: false,
        selected_rows: [],
        roleName: c.getRoleName(),
        userData: c.session().user,
        userWorking: c.session().me,
      };
    },

    async created() {
      this.$store.commit("toggleLoading", true);
      this.companies = [...this.companies, ...c.session().infos.companies];
      this.invoice_requests = [
        ...this.invoice_requests,
        ...c.session().infos.invoice_requests,
      ];
      this.invoice_status = [
        ...this.invoice_status,
        ...c.session().infos.invoice_status,
      ];
      const { products } = c.session().infos;
      this.products = this.products.concat(products);
      const response = await c.g(`/api/master/province-business/all`);
      this.provinces = [...this.provinces, ...response];
      const teams = await c.g(`/api/team-sale`);
      this.teams = [...this.teams, ...teams];
      this.bind_data();
      this.get_data(this.table_option.current_page);
    },
    watch: {
      'table_option.per_page': function () {
        this.table_option.current_page = 1;
        this.get_data(this.table_option.current_page);
      }
    },
    methods: {
      checkPermission,
      checkPerDelete(item) {
        return [item.created_by, item.leader_id, item.sale_id].includes(this.userData.id)
      },
      checkPerEdit(item) {
        return [item.created_by, item.leader_id, item.sale_id].includes(this.userData.id)
      },
      async changeProvince() {
        this.$store.commit("toggleLoading", true);
        this.filter.district_id = "";
        this.districts = [{ district_id: "", name: "Chọn" }];
        const province = this.provinces.find(
          (x) => x.province_id === this.filter.province_id
        );
        if (province && this.filter.province_id) {
          this.districts = [
            { district_id: "", name: "Chọn" },
            ...province.districtBusiness,
          ];
        }
        this.get_data();
        this.$store.commit("toggleLoading", false);
      },
      get_data(page) {
        this.$store.commit("toggleLoading", true);
        c.g(`/api/master/contract?page=${page}&per_page=${this.table_option.per_page}`, this.filter)
          .then((response) => {
            this.sum_contract_value = response.sum_contract_value;
            this.sum_received_money = response.sum_received_money;
            this.sum_payment_amount = response.sum_payment_amount;
            this.sum_debt = response.sum_debt;
            this.items = response.data.data;
            this.table_option.current_page = response.data.current_page;
            this.table_option.per_page = response.data.per_page;
            this.table_option.from = response.data.from;
            this.table_option.to = response.data.to;
            this.table_option.total = response.data.total;
            this.$store.commit("toggleLoading", false);
            this.items.forEach(d => {
              this.selected_rows[d.id] = false;
            });
          })
          .catch((e) => {
            this.$store.commit("toggleLoading", false);
          });
      },
      bind_data() {
        this.columns = [
          { key: "id", label: "STT", tdClass: "text-center" },
          {
            key: "clients.province_business_market.name",
            label: "Tỉnh/Thành",
            thClass: "text-left",
          },
          {
            key: "clients.district_business_market.name",
            label: "Quận/Huyện",
            thClass: "text-left",
          },
          {
            key: "clients.code",
            label: "Mã Khách hàng",
            thClass: "text-left",
          },
          {
            key: "clients.name",
            label: "Tên KH",
            thClass: "text-left",
          },
          {
            key: "product.code",
            label: "Tên SP",
            thClass: "text-left",
            tdClass: "text-left",
          },
          {
            key: "contract_value",
            label: "Giá trị hợp đồng",
            thClass: "text-right",
            tdClass: "text-right",
          },
          {
            key: "payment_amount",
            label: "Giá trị thực",
            thClass: "text-right",
            tdClass: "text-right",
          },
          {
            key: "received_money",
            label: "Đã thu",
            thClass: "text-right",
            tdClass: "text-right",
          },
          {
            key: "debt",
            label: "Công nợ",
            thClass: "text-right",
            tdClass: "text-right",
          },
          {
            key: "created_at",
            label: "Ngày tạo HĐ",
            thClass: "text-center",
            tdClass: "text-center",
          },
          {
            key: "sale_date",
            label: "Ngày tính doanh số",
            thClass: "text-center",
            tdClass: "text-center",
          },
          {
            key: "team",
            label: "Team",
            thClass: "text-left",
            tdClass: "text-left",
          },
          {
            key: "invoice_request",
            label: "Yêu cầu XHĐ",
            thClass: "text-left",
            tdClass: "text-left",
          },
          {
            key: "invoice_status",
            label: "Trạng thái XHĐ",
            thClass: "text-left",
            tdClass: "text-left",
          },
        ];
        if (checkPermission(["contract_list_management", "contract_list"])) {
          this.columns = this.columns.concat([
            {
              key: "action",
              label: "",
              class: "actions text-center",
              thClass: "th",
            },
          ]);
        }
      },
      delete_row(item) {
        this.$swal({
          text: "Bạn có chắc chắn thực hiện thao tác xoá hợp đồng này?",
          type: "warning",
          showCancelButton: true,
          cancelButtonText: "Huỷ",
          confirmButtonText: "Xoá",
          padding: "0.25em",
        }).then((result) => {
          if (result.value) {
            this.$store.commit("toggleLoading", true);
            c.p(`/api/contract/destroy/${item.id}`)
              .then((response) => {
                this.get_data();
                this.$swal({
                  icon: "success",
                  title: "Xóa thành công",
                  text: "",
                  padding: "2em",
                });
                this.$store.commit("toggleLoading", false);
              })
              .catch((e) => {
                this.$store.commit("toggleLoading", false);
              });
          }
        });
      },
      change_page(page) {
        this.get_data(page);
      },
      reset() {
        this.filter = {
          province_id: "",
          district_id: "",
          product_id: "",
          school_name: "",
          client_code: "",
          team: "",
          company: "",
          invoice_request: "",
          invoice_status: "",
          created_at: "",
          month: "",
          year: "",
        };
        this.get_data();
      },
      change_filter() {
        this.get_data();
      },
      download_file() {
        c.ep(`/api/contract/downloadFileSample`).then((response) => {
          saveAs(response, "contract_sample.xlsx");
        });
      },
      async download_report() {
        if (
          (!this.filter.team ||
            !this.filter.created_at ||
            !this.filter.company) && !this.roleName == "accounting"
        ) {
          this.$store.commit("setSnackBar", {
            type: "danger",
            msg: "Vui lòng chọn Team, Công ty, Ngày tạo hợp đồng!",
          });
        } else {
          if (this.roleName == "accounting") this.filter.selected_rows = this.selected_rows;
          c.ep(`/api/export-contract`, this.filter).then((response) => {
            saveAs(response, "Report.xlsx");
          });
        }
      },

      clear_selection() {
        this.is_select_all = false;
        this.selected_rows = {};
      },
      toggleSelectAll() {
        this.items.forEach(row => {
          this.$set(this.selected_rows, row.id, this.is_select_all);
        });
      },
      contract_checked(id) {
        this.$set(this.selected_rows, id, this.selected_rows[id]);
        this.updateSelectAll();
      },
      updateSelectAll() {
        const total = this.items.length;
        const checked = Object.values(this.selected_rows).filter(value => value).length;
        this.is_select_all = total === checked;
      },
      select_all() {
        this.selected_rows = this.$refs.contract_table.paginatedItems.map(d => {
          return d.id;
        });
        if (!this.is_select_all) {
          this.clear_selection();
        }
        this.check_select_all();
      },
      check_select_all() {
        let ids = this.$refs.contract_table.paginatedItems.map(d => {
          return d.id;
        });
        this.is_select_all = false;
        if (ids.length == this.selected_rows.length) {
          this.is_select_all = true;
        }
      },

      clear_selection() {
        this.is_select_all = false;
        this.selected_rows = [];
      },
    },
  };
</script>
<style>
  .custom-table .contract tr th {
    font-size: 12px !important;
  }

  .custom-table .contract tr td {
    font-size: 10px !important;
  }

  .mx-datepicker {
    width: 100%;
  }

  .mx-input {
    height: 45px;
    border-radius: 5px;
  }

  .mx-calendar-header {
    display: none;
  }

  .btn.btn-sm {
    padding: .3rem .6rem !important;
  }

  .header-search {
      padding: 15px 25px 10px 0px ;
      flex: 1;
      text-align: right;
  }

  input[name="date"] {
      height: 39px !important;
  }
</style>
