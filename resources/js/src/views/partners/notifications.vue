<template>
  <div class="layout-px-spacing">
    <portal to="breadcrumb">
      <ul class="navbar-nav flex-row">
        <li>
          <div class="page-header">
            <nav class="breadcrumb-one" aria-label="breadcrumb">
              <ol class="breadcrumb">
                <li class="breadcrumb-item">
                  <router-link tag="a" to="/partners/dashboard">
                    <i data-feather="home"></i>
                  </router-link>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                  <span>Thông báo</span>
                </li>
              </ol>
            </nav>
          </div>
        </li>
      </ul>
    </portal>

    <div class="row layout-top-spacing">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 layout-spacing">
        <div class="widget widget-card-four">
          <div class="widget-content">
            <div class="w-header">
              <div class="w-title">
                <h5>Thông báo</h5>
              </div>
              <div class="task-action" v-if="unreadCount > 0">
                <button class="btn btn-sm btn-primary" @click="markAllAsRead">
                  Đánh dấu đã đọc tất cả
                </button>
              </div>
            </div>

            <div class="w-content">
              <div v-if="loading" class="text-center p-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">Loading...</span>
                </div>
              </div>

              <div v-else-if="notifications.length === 0" class="text-center p-5">
                <p class="mb-0">Không có thông báo</p>
              </div>

              <div v-else class="notification-list">
                <div
                  v-for="notification in notifications"
                  :key="notification.id"
                  class="notification-item"
                  :class="{ 'unread': !notification.read_at }"
                  @click="markAsRead(notification)"
                >
                  <div class="notification-icon">
                    <div class="icon-svg" :class="getIconClass(notification.data.type)">
                      <i :data-feather="getIcon(notification.data.type)"></i>
                    </div>
                  </div>
                  <div class="notification-content">
                    <h6>{{ notification.data.title }}</h6>
                    <p>{{ notification.data.body }}</p>
                    <div class="notification-meta">
                      <span class="time">{{ formatDate(notification.created_at) }}</span>
                      <span v-if="!notification.read_at" class="badge badge-primary">Mới</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="pagination-container mt-4" v-if="totalPages > 1">
                <b-pagination
                  v-model="currentPage"
                  :total-rows="totalItems"
                  :per-page="perPage"
                  @change="changePage"
                  align="center"
                ></b-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import c from "@/helpers/common";
import feather from "feather-icons";
import moment from "moment";

export default {
  data() {
    return {
      notifications: [],
      unreadCount: 0,
      loading: true,
      currentPage: 1,
      totalItems: 0,
      perPage: 20,
      totalPages: 0
    };
  },
  mounted() {
    this.fetchNotifications();
  },
  updated() {
    feather.replace();
  },
  methods: {
    async fetchNotifications() {
      this.loading = true;
      try {
        const response = await c.g(`/api/partners/notifications?page=${this.currentPage}`);
        if (response.success) {
          this.notifications = response.data.data;
          this.totalItems = response.data.total;
          this.currentPage = response.data.current_page;
          this.perPage = response.data.per_page;
          this.totalPages = Math.ceil(this.totalItems / this.perPage);
          
          // Count unread notifications
          this.unreadCount = this.notifications.filter(n => !n.read_at).length;
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        this.loading = false;
        this.$nextTick(() => {
          feather.replace();
        });
      }
    },
    async markAsRead(notification) {
      if (!notification.read_at) {
        try {
          await c.p(`/api/partners/notifications/${notification.id}/read`);
          notification.read_at = new Date();
          this.unreadCount--;
        } catch (error) {
          console.error('Error marking notification as read:', error);
        }
      }
    },
    async markAllAsRead() {
      try {
        await c.p('/api/partners/notifications/read-all');
        this.notifications.forEach(notification => {
          notification.read_at = new Date();
        });
        this.unreadCount = 0;
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
      }
    },
    changePage(page) {
      this.currentPage = page;
      this.fetchNotifications();
    },
    formatDate(date) {
      return moment(date).format('DD/MM/YYYY HH:mm');
    },
    getIcon(type) {
      switch (type) {
        case 'license_activated':
          return 'key';
        case 'account_created':
          return 'user-plus';
        case 'license_expired':
          return 'alert-triangle';
        default:
          return 'bell';
      }
    },
    getIconClass(type) {
      switch (type) {
        case 'license_activated':
          return 'success';
        case 'account_created':
          return 'primary';
        case 'license_expired':
          return 'warning';
        default:
          return 'info';
      }
    }
  }
};
</script>

<style scoped>
.notification-list {
  max-height: 600px;
  overflow-y: auto;
}
.notification-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #e0e6ed;
  cursor: pointer;
  transition: background-color 0.3s;
}
.notification-item:hover {
  background-color: #f8f9fa;
}
.notification-item.unread {
  background-color: rgba(27, 85, 226, 0.05);
}
.notification-icon {
  margin-right: 15px;
}
.icon-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
}
.icon-svg.success {
  background-color: rgba(0, 150, 136, 0.1);
  color: #009688;
}
.icon-svg.primary {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}
.icon-svg.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}
.icon-svg.info {
  background-color: rgba(0, 188, 212, 0.1);
  color: #00bcd4;
}
.notification-content {
  flex: 1;
}
.notification-content h6 {
  margin-bottom: 5px;
  font-weight: 600;
}
.notification-content p {
  margin-bottom: 5px;
  color: #515365;
}
.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #888ea8;
}
</style>
