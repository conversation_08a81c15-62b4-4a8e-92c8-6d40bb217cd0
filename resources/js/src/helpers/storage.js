/**
 * Storage Service
 * A compatibility layer that provides localStorage-like API for IndexedDB
 */

import indexedDB from './indexedDB';

/**
 * Check if IndexedDB is supported in the browser
 * @returns {boolean} - True if IndexedDB is supported
 */
const isIndexedDBSupported = () => {
    return 'indexedDB' in window;
};

/**
 * Get an item from storage
 * @param {string} key - The key to retrieve
 * @returns {Promise} - Promise that resolves with the parsed value
 */
const getItem = async (key) => {
    try {
        if (isIndexedDBSupported()) {
            const value = await indexedDB.get(key);

            // If value is undefined or null, try localStorage as fallback
            if (value === undefined || value === null) {
                const localValue = localStorage.getItem(key);

                // If it looks like JSON, try to parse it
                if (localValue && (localValue.startsWith('{') || localValue.startsWith('['))) {
                    try {
                        return JSON.parse(localValue);
                    } catch (parseError) {
                        return localValue;
                    }
                }

                return localValue;
            }

            return value;
        } else {
            const value = localStorage.getItem(key);

            // If it looks like J<PERSON><PERSON>, try to parse it
            if (value && (value.startsWith('{') || value.startsWith('['))) {
                try {
                    return JSON.parse(value);
                } catch (parseError) {
                    return value;
                }
            }

            return value;
        }
    } catch (error) {
        console.error('Error getting item from storage:', error);
        // Fallback to localStorage
        const value = localStorage.getItem(key);

        // If it looks like JSON, try to parse it
        if (value && (value.startsWith('{') || value.startsWith('['))) {
            try {
                return JSON.parse(value);
            } catch (parseError) {
                return value;
            }
        }

        return value;
    }
};

/**
 * Set an item in storage
 * @param {string} key - The key to set
 * @param {any} value - The value to store
 * @returns {Promise} - Promise that resolves when the value is stored
 */
const setItem = async (key, value) => {
    try {
        // Always update localStorage for backward compatibility
        if (value === null || value === undefined) {
            localStorage.removeItem(key);
        } else if (typeof value === 'object') {
            localStorage.setItem(key, JSON.stringify(value));
        } else {
            localStorage.setItem(key, String(value));
        }

        // Then update IndexedDB if supported
        if (isIndexedDBSupported()) {
            await indexedDB.set(key, value);
            return true;
        }

        return true;
    } catch (error) {
        console.error('Error setting item in storage:', error);

        // Ensure localStorage is updated even if IndexedDB fails
        try {
            if (value === null || value === undefined) {
                localStorage.removeItem(key);
            } else if (typeof value === 'object') {
                localStorage.setItem(key, JSON.stringify(value));
            } else {
                localStorage.setItem(key, String(value));
            }
            return true;
        } catch (localError) {
            console.error('Error setting item in localStorage fallback:', localError);
            return false;
        }
    }
};

/**
 * Remove an item from storage
 * @param {string} key - The key to remove
 * @returns {Promise} - Promise that resolves when the value is removed
 */
const removeItem = async (key) => {
    try {
        // Always remove from localStorage for backward compatibility
        localStorage.removeItem(key);

        // Then remove from IndexedDB if supported
        if (isIndexedDBSupported()) {
            await indexedDB.remove(key);
        }

        return true;
    } catch (error) {
        console.error('Error removing item from storage:', error);

        // Ensure localStorage is updated even if IndexedDB fails
        try {
            localStorage.removeItem(key);
            return true;
        } catch (localError) {
            console.error('Error removing item from localStorage fallback:', localError);
            return false;
        }
    }
};

/**
 * Clear all items from storage
 * @returns {Promise} - Promise that resolves when all values are cleared
 */
const clear = async () => {
    try {
        // Always clear localStorage for backward compatibility
        localStorage.clear();

        // Then clear IndexedDB if supported
        if (isIndexedDBSupported()) {
            await indexedDB.clear();
        }

        return true;
    } catch (error) {
        console.error('Error clearing storage:', error);

        // Ensure localStorage is cleared even if IndexedDB fails
        try {
            localStorage.clear();
            return true;
        } catch (localError) {
            console.error('Error clearing localStorage fallback:', localError);
            return false;
        }
    }
};

/**
 * Get a parsed JSON item from storage
 * @param {string} key - The key to retrieve
 * @returns {Promise} - Promise that resolves with the parsed value
 */
const getObject = async (key) => {
    try {
        const value = await getItem(key);
        if (value === null || value === undefined) {
            return null;
        }

        // If it's already an object and not null, return it
        if (typeof value === 'object' && value !== null) {
            return value;
        }

        // Otherwise, try to parse it if it's a string
        if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            } catch (parseError) {
                // If parsing fails, return the original value
                return value;
            }
        }

        // Return the value as is if it's not a string or object
        return value;
    } catch (error) {
        console.error('Error getting object from storage:', error);

        // Fallback to localStorage
        try {
            const localValue = localStorage.getItem(key);
            if (localValue === null || localValue === undefined) {
                return null;
            }

            return JSON.parse(localValue);
        } catch (localError) {
            console.error('Error parsing JSON from localStorage fallback:', localError);
            return null;
        }
    }
};

/**
 * Set a JSON object in storage
 * @param {string} key - The key to set
 * @param {object} value - The object to store
 * @returns {Promise} - Promise that resolves when the value is stored
 */
const setObject = async (key, value) => {
    try {
        // Store the object directly in IndexedDB
        // IndexedDB can store objects directly, unlike localStorage
        await setItem(key, value);

        // Also update localStorage for backward compatibility
        if (typeof value === 'object') {
            localStorage.setItem(key, JSON.stringify(value));
        } else {
            localStorage.setItem(key, value);
        }
    } catch (error) {
        console.error('Error setting object in storage:', error);

        // Fallback to localStorage
        if (typeof value === 'object') {
            localStorage.setItem(key, JSON.stringify(value));
        } else {
            localStorage.setItem(key, value);
        }
    }
};

export default {
    getItem,
    setItem,
    removeItem,
    clear,
    getObject,
    setObject
};
