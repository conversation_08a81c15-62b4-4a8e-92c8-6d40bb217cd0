import axios from "axios";
import c from "./common";
import storage from "./storage";

export async function fetchInfoData(key) {
    try {
        const response = await c.p(`/api/partners/account/get_project_info/${key}`);
        console.log("apiData = " + JSON.stringify(response));

        let localParsed = await storage.getObject("info_${key}");

        if (
            !localParsed ||
            hasDataChanged(response, localParsed)
        ) {
            console.log("Change data.......");
            // Save to storage
            const newStore = {
                data: response,
                updated_at: new Date().toISOString(),
            };
            await storage.setObject(`info_${key}`, newStore);
            return JSON.stringify(response);
        } else {
            return JSON.stringify(localParsed);
        }
    } catch (error) {
        console.error("Error fetching data:", error);
        return null;
    }
}

export function partner_currentUser() {
    const user = localStorage.getItem("partner");

    if (!user) {
        return null;
    }

    return JSON.parse(user);
}

// Partner
export function partner_login(credential) {
    return new Promise((res, rej) => {
        axios
            .post("/api/partners/auth/login", credential)
            .then((result) => {
                res(result.data);
            })
            .catch((err) => {
                rej("Tài khoản không hợp lệ.");
            });
    });
}

export function partner_logout() {
    let token = c.partner_token();
    axios.defaults.headers.common["Authorization"] = "bearer " + token;
    axios
        .post("/api/partners/auth/logout")
        .then((response) => {})
        .catch((e) => {
            c.lg(e, "catch");
        });
}

export function getProjectInfo() {
    try {
        const response = c.p(`/api/partners/account/get_project_info/qlmn`);
        console.log("apiData = " + JSON.stringify(response));

        let localParsed = storage.getObject("info");

        if (!localParsed || hasDataChanged(response, localParsed)) {
            console.log("Change data.......");
            // Save to storage
            const newStore = {
                data: response,
                updated_at: new Date().toISOString(),
            };
            storage.setObject("info", newStore);
            this.info = JSON.stringify(response);
        } else {
            this.info = JSON.stringify(localParsed);
        }
        //console.log("this.info = ", this.info);
    } catch (error) {
        console.error("Error fetching data:", error);
    }
}

export function hasDataChanged(newData, oldData) {
    // You can improve this with deep comparison or by checking version/timestamp
    return JSON.stringify(newData) !== JSON.stringify(oldData);
}
