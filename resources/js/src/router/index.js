import Vue from "vue";
import VueRouter from "vue-router";
import Home from "../views/index.vue";
import store from "../store";
import Dashboard from "../views/dashboard/index.vue";
Vue.use(VueRouter);

const routes = [
    // Đăng nhập
    {
        path: "/login",
        name: "login-form",
        component: () => import("../views/auth/login_boxed.vue"),
        meta: { layout: "auth", requiresAuth: false },
    },
    //dashboard public
    {
        path: "/dashboard-public",
        name: "dashboard_public",
        component: () => import("../views/auth/dashboard_public.vue"),
        meta: { layout: "auth", requiresAuth: false },
    },
    //<PERSON><PERSON> s<PERSON>ch riêng tư
    {
        path: "/privacy-policy",
        name: "privacy_policy",
        component: () => import("../views/auth/privacy_policy.vue"),
        meta: { layout: "auth", requiresAuth: false },
    },
    //dashboard Lúc đăng nhập v<PERSON><PERSON> <PERSON> thống
    {
        path: "/dashboard",
        name: "dashboard",
        component: () => import("../views/auth/dashboard_public.vue"),
        meta: { layout: "app", requiresAuth: true },
    },
    // Trang chủ
    {
        path: "/",
        name: "Home",
        component: Dashboard,
        meta: { requiresAuth: true, layout: "app" },
    },
    //Profile_me
    {
        path: "/users/profile",
        name: "profile",
        component: () => import("../views/users/profile_me.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/happy-birthday",
        name: "happy_birthday",
        component: () => import("../views/users/happy_birthday.vue"),
        meta: { requiresAuth: true },
    },
    // Quản lý nhân sự
    {
        path: "/user",
        name: "user.list",
        component: () => import("../views/users/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/user/status",
        name: "user.status",
        component: () => import("../views/users/dashboard_status.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/user/import",
        name: "user.import",
        component: () => import("../views/users/import.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/user/add",
        name: "user.add",
        component: () => import("../views/users/add.vue"),
        meta: { requiresAuth: true, children: true, parent: "/user" },
    },
    {
        path: "/user/:id/edit",
        name: "user.edit",
        component: () => import("../views/users/edit.vue"),
        meta: { requiresAuth: true, children: true, parent: "/user" },
    },
    {
        path: "/user/:id",
        name: "user.detail",
        component: () => import("../views/users/detail.vue"),
        meta: { requiresAuth: true, children: true, parent: "/user" },
    },

    // phòng ban
    {
        path: "/department",
        name: "department.list",
        component: () => import("../views/department/list.vue"),
        meta: { requiresAuth: true },
    },
    // bộ phận
    {
        path: "/position",
        name: "position.list",
        component: () => import("../views/position/list.vue"),
        meta: { requiresAuth: true },
    },
    // sản phẩm
    {
        path: "/product",
        name: "product.list",
        component: () => import("../views/product/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/product/add",
        name: "product.add",
        component: () => import("../views/product/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/product" },
    },
    {
        path: "/product/edit/:id",
        name: "product.edit",
        component: () => import("../views/product/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/product" },
    },
    {
        path: "/product-category",
        name: "product.category",
        component: () => import("../views/product/category.vue"),
        meta: { requiresAuth: true },
    },

    // Cấu hình khu vực kinh doanh
    {
        path: "/config-business-area",
        name: "config-business-area",
        component: () =>
            import("../views/business_area/config-business-area.vue"),
        meta: { requiresAuth: true },
    },

    // Nhóm quyền
    {
        path: "/roles",
        name: "roles.list",
        component: () => import("../views/roles/list.vue"),
        meta: { requiresAuth: true },
    },
    //Quản lý khách hàng
    {
        path: "/client",
        name: "client",
        component: () => import("../views/clients/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/client/add",
        name: "client.add",
        component: () => import("../views/clients/add.vue"),
        meta: { requiresAuth: true, children: true, parent: "/client" },
    },
    {
        path: "/client/edit/:id",
        name: "client.edit",
        component: () => import("../views/clients/edit.vue"),
        meta: { requiresAuth: true, children: true, parent: "/client" },
    },
    {
        path: "/client/import",
        name: "client.import",
        component: () => import("../views/clients/import.vue"),
        meta: { requiresAuth: true, children: true, parent: "/client" },
    },
    // hợp đồng
    {
        path: "/contract",
        name: "contract",
        component: () => import("../views/contracts/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/contract/add",
        name: "contract.add",
        component: () => import("../views/contracts/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/payment",
        name: "contract.payment",
        component: () => import("../views/contracts/payment.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/add/client/:idClient",
        name: "contract.add.client",
        component: () => import("../views/contracts/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/edit-contract-review/:id",
        name: "contract.edit-contract-review",
        component: () => import("../views/contracts/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/details/:id",
        name: "contract.details",
        component: () => import("../views/contracts/details.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/import",
        name: "contract.import",
        component: () => import("../views/contracts/import.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/review-list",
        name: "contract.review-list",
        component: () => import("../views/contracts/review_list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/review-detail/:id",
        name: "contract.review-detail",
        component: () => import("../views/contracts/review_detail.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/review-contract/:id",
        name: "contract.review-contract",
        component: () => import("../views/contracts/review_contract.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/edit/:id",
        name: "contract.edit",
        component: () => import("../views/contracts/edit.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/contract/review-contract-form",
        name: "contract.review-contract-form",
        component: () => import("../views/contracts/review_contract_form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/contract" },
    },
    {
        path: "/debt",
        name: "debt",
        component: () => import("../views/debts/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/expense",
        name: "expense",
        component: () => import("../views/expenses/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/expense/add",
        name: "expense.add",
        component: () => import("../views/expenses/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/expense" },
    },
    {
        path: "/expense/edit/:id",
        name: "expense.edit",
        component: () => import("../views/expenses/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/expense" },
    },
    {
        path: "/payment",
        name: "payment",
        component: () => import("../views/payments/list.vue"),
        meta: { requiresAuth: true },
    },
    // changeLog
    {
        path: "/changelog-list",
        name: "changelog.list",
        component: () => import("../views/changelog/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/changelog-management",
        name: "changelog.management",
        component: () => import("../views/changelog/management.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/changelog/add",
        name: "changelog.add",
        component: () => import("../views/changelog/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/changelog" },
    },
    {
        path: "/changelog/detail/:id",
        name: "changelog.detail",
        component: () => import("../views/changelog/detail.vue"),
        meta: { requiresAuth: true, children: true, parent: "/changelog" },
    },
    {
        path: "/changelog/edit/:id",
        name: "changelog.edit",
        component: () => import("../views/changelog/form.vue"),
        meta: { requiresAuth: true, children: true, parent: "/changelog" },
    },
    {
        path: "/changelog-detail/:id",
        name: "changelog.log_detail",
        component: () => import("../views/changelog/log_detail.vue"),
        meta: { requiresAuth: true, children: true, parent: "/changelog" },
    },
    // report
    {
        path: "/report",
        name: "report",
        component: () => import("../views/reports/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/report/report-by-product",
        name: "report-by-product",
        component: () => import("../views/reports/report_by_product.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/report-by-team",
        name: "report-by-team",
        component: () => import("../views/reports/report_by_team.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/report-by-market",
        name: "report-by-market",
        component: () => import("../views/reports/report_by_market.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/sale-report-week",
        name: "report_sale_report_week",
        component: () => import("../views/reports/sale_report_week.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/sale-report-product",
        name: "report_sale_report_product",
        component: () => import("../views/reports/sale_report_product.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/sale-report-debt",
        name: "report_sale_report_debt",
        component: () => import("../views/reports/sale_report_debt.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/sale-report-expire",
        name: "report_sale_report_expire",
        component: () => import("../views/reports/sale_report_expire.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/call-center",
        name: "report_call_center",
        component: () => import("../views/reports/call_center.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/sales",
        name: "report_sales",
        component: () => import("../views/reports/sales_three_year.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/team-sales",
        name: "report_team_sales",
        component: () => import("../views/reports/team_sales.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    {
        path: "/report/actual-sales-and-goal-sales",
        name: "report_actual_sales_and_goal_sales.vue",
        component: () =>
            import("../views/reports/actual_sales_and_goal_sales.vue"),
        meta: { requiresAuth: true, children: true, parent: "/report" },
    },
    // KPI
    {
        path: "/master-kpi",
        name: "master_kpi.list",
        component: () => import("../views/kpi/master_kpi.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/assessment-list-kpi",
        name: "kpi.assessment-list",
        component: () => import("../views/kpi/assessment/list.vue"),
        meta: {
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/kpi/assessment-details-kpi/:kpiSummaryId",
        name: "kpi.assessment-details-kpi",
        component: () => import("../views/kpi/assessment/details.vue"),
        meta: {
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/list-kpi-register",
        name: "kpi.list-register",
        component: () => import("../views/kpi/register/list.vue"),
        meta: {
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/register-kpi/create",
        name: "register_kpi.register",
        component: () => import("../views/kpi/register/register-form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/register-kpi/:kpiSummaryId",
        name: "register_kpi.details",
        component: () => import("../views/kpi/register/details.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/register-kpi/:kpiSummaryId/edit",
        name: "register_kpi.edit",
        component: () => import("../views/kpi/register/register-form.vue"),
        meta: { requiresAuth: true },
    },
    //Quản lý công tác
    {
        path: "/business-plan-list",
        name: "business_plan.list",
        component: () => import("../views/businesses/business_plans/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/business-plan-form",
        name: "business_plan.create",
        component: () => import("../views/businesses/business_plans/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/business-plan-form/edit/:id",
        name: "business_plan.edit",
        component: () => import("../views/businesses/business_plans/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/business-report-list",
        name: "business_report.list",
        component: () =>
            import("../views/businesses/business_reports/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/business-report-form",
        name: "business_report.create",
        component: () =>
            import("../views/businesses/business_reports/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/business-report-form/edit/:id",
        name: "business_report.edit",
        component: () =>
            import("../views/businesses/business_reports/form.vue"),
        meta: { requiresAuth: true },
    },
    // Quản lý công việc
    {
        path: "/tasks-management/projects",
        name: "tasks.projects.list",
        component: () => import("../views/tasks/projects/list.vue"),
        meta: {
            menuStyle: "horizontal",
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/tasks-management/projects/:id/config",
        name: "tasks.projects.config",
        component: () => import("../views/tasks/projects/config-project.vue"),
        meta: {
            menuStyle: "horizontal",
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/tasks-management/projects/:id/tickets",
        name: "tasks.projects.tickets",
        component: () => import("../views/tasks/tickets/list.vue"),
        meta: {
            menuStyle: "horizontal",
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/tasks-management/projects/:id/tickets/:ticketId",
        name: "tasks.projects.tickets.details",
        component: () => import("../views/tasks/tickets/list.vue"),
        meta: {
            menuStyle: "horizontal",
            requiresAuth: true,
            children: false,
        },
    },
    {
        path: "/tasks-management/projects/:id/report",
        name: "tasks.projects.report",
        component: () => import("../views/tasks/projects/report.vue"),
        meta: {
            menuStyle: "horizontal",
            requiresAuth: true,
            children: false,
        },
    },

    //thứ 4 chia sẻ
    {
        path: "/wednesday-share",
        name: "wednesday-share.list",
        component: () => import("../views/wednesday_shares/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/wednesday-share/add",
        name: "wednesday-share.add",
        component: () => import("../views/wednesday_shares/form.vue"),
        meta: {
            requiresAuth: true,
            children: true,
            parent: "/wednesday-share",
        },
    },
    {
        path: "/wednesday-share/edit/:id",
        name: "wednesday-share.edit",
        component: () => import("../views/wednesday_shares/form.vue"),
        meta: {
            requiresAuth: true,
            children: true,
            parent: "/wednesday-share",
        },
    },
    // Cấu hình hệ thống
    {
        path: "/setting-happy-text",
        name: "setting-happy-text",
        component: () => import("../views/settings/happy_text_list.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/config-happy-birthday",
        name: "config-happy-birthday",
        component: () => import("../views/settings/happy_birthday.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/setting-receive-email",
        name: "setting-receive-email",
        component: () => import("../views/settings/receive-email.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/setting-feedback-object",
        name: "setting-feedback-object",
        component: () => import("../views/settings/feedback-object.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/setting-kpi-assessment-time",
        name: "setting-kpi-assessment-time",
        component: () => import("../views/settings/kpi-assessment-time.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/setting-explain",
        name: "setting-explain",
        component: () => import("../views/settings/explain.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    //call-center
    {
        path: "/call-center",
        name: "call_center.list",
        component: () => import("../views/call_center/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/call-center/choose-client/:id",
        name: "call_center.choose_client",
        component: () => import("../views/call_center/choose_client.vue"),
        meta: { requiresAuth: true },
    },
    // quản lý danh mục hệ thống
    {
        path: "/schools",
        name: "schools.list",
        component: () => import("../views/schools/list.vue"),
        meta: { requiresAuth: true },
    },
    // Notification
    {
        path: "/notifications",
        name: "notifications.list",
        component: () => import("../views/notifications/list.vue"),
        meta: { requiresAuth: true },
    },
    // Holiday
    {
        path: "/holiday",
        name: "holiday.list",
        component: () => import("../views/holiday/list.vue"),
        meta: { requiresAuth: true },
    },
    //work day symbol
    {
        path: "/work-day-symbol",
        name: "work_day_symbol.list",
        component: () => import("../views/work_day_symbols/list.vue"),
        meta: { requiresAuth: true },
    },
    //quarterly goals
    {
        path: "/quarterly-goals",
        name: "quarterly_goals.list",
        component: () => import("../views/quarterly_goals/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/quarterly-goals/add",
        name: "quarterly_goals.add",
        component: () => import("../views/quarterly_goals/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/quarterly-goals/edit/:id",
        name: "quarterly_goals.edit",
        component: () => import("../views/quarterly_goals/form.vue"),
        meta: { requiresAuth: true },
    },
    //month goals
    {
        path: "/month-goals",
        name: "month_goals.list",
        component: () => import("../views/month_goals/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/month-goals/add",
        name: "month_goals.add",
        component: () => import("../views/month_goals/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/month-goals/edit/:id",
        name: "month_goals.edit",
        component: () => import("../views/month_goals/form.vue"),
        meta: { requiresAuth: true },
    },
    // time sheets
    {
        path: "/time-sheet",
        name: "time_sheet.list",
        component: () => import("../views/time_sheet/list.vue"),
        meta: { requiresAuth: true },
    },
    // checkin - checkout
    {
        path: "/checkin-checkout",
        name: "time_sheet.inout",
        component: () => import("../views/time_sheet/inout.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/checkin-checkout/explain",
        name: "time_sheet.explain",
        component: () => import("../views/time_sheet/explain.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/explain/miss",
        name: "time_sheet.explainmiss",
        component: () => import("../views/time_sheet/explainmiss.vue"),
        meta: { requiresAuth: true },
    },
    // xác nhận công
    {
        path: "/checkin-checkout-reject",
        name: "time_sheet.inout_reject",
        component: () => import("../views/time_sheet/inout_reject.vue"),
        meta: { requiresAuth: true },
    },
    // hanet-cam-user
    {
        path: "/hanet-cam-user",
        name: "time_sheet.cam_user",
        component: () => import("../views/time_sheet/hanet_cam_user.vue"),
        meta: { requiresAuth: true },
    },
    // checkin - checkout - online
    {
        path: "/checkin-checkout-online",
        name: "time_sheet.inoutsonline",
        component: () => import("../views/time_sheet/inoutsonline.vue"),
        meta: { requiresAuth: true },
    },
    // danh mục ngày nghỉ phép
    {
        path: "/absence-letter-type",
        name: "absence_letter.list_type",
        component: () => import("../views/absence_letter/list_type.vue"),
        meta: { requiresAuth: true },
    },
    // quản lý phép năm nhân viên
    {
        path: "/absence-year",
        name: "absence_year.list",
        component: () => import("../views/absence_year/list.vue"),
        meta: { requiresAuth: true },
    },
    // absence letter
    {
        path: "/absence-letters",
        name: "absence_letter.list",
        component: () => import("../views/absence_letters/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/absence-letters/add",
        name: "absence_letter.add",
        component: () => import("../views/absence_letters/add.vue"),
        meta: {
            requiresAuth: true,
            children: true,
            parent: "/absence-letters",
        },
    },
    {
        path: "/absence-letters/explain",
        name: "absence_letter.explain",
        component: () => import("../views/absence_letters/explain.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/absence-letters/edit/:id",
        name: "absence_letter.edit",
        component: () => import("../views/absence_letters/edit.vue"),
        meta: {
            requiresAuth: true,
            children: true,
            parent: "/absence-letters",
        },
    },
    // Khảo sát
    {
        path: "/survey/category",
        name: "survey.category.list",
        component: () => import("../views/survey/category/list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/survey" },
    },
    {
        path: "/survey/question",
        name: "survey.question.list",
        component: () => import("../views/survey/question/list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/survey" },
    },
    {
        path: "/survey/exam",
        name: "survey.exam.list",
        component: () => import("../views/survey/exam/list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/survey" },
    },
    {
        path: "/survey-list",
        name: "survey.exam.survey-list",
        component: () => import("../views/survey/exam/history.vue"),
        meta: { requiresAuth: true, children: false },
    },
    {
        path: "/survey/exam/history",
        name: "survey.exam.history",
        component: () => import("../views/survey/exam/history.vue"),
        meta: { requiresAuth: true, children: true, parent: "/survey" },
    },
    {
        path: "/survey/exam/:id/do-exam",
        name: "survey.exam.do-exam",
        component: () => import("../views/survey/exam/do-exam.vue"),
        meta: {
            layout: "auth",
            requiresAuth: true,
            children: true,
            parent: "/survey",
        },
    },
    {
        path: "/survey/exam/:id/details-history",
        name: "survey.exam.details-history",
        component: () => import("../views/survey/exam/details-history.vue"),
        meta: {
            layout: "auth",
            requiresAuth: true,
            children: true,
            parent: "/survey",
        },
    },
    // KidsEnglish
    {
        path: "/sales-packages",
        name: "kidsenglish.sales-packages.list",
        component: () => import("../views/kidsenglish/sales_packages/list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/kidsenglish" },
    },
    {
        path: "/kidsenglish/account-allocation",
        name: "kidsenglish.account-allocation.list",
        component: () =>
            import("../views/kidsenglish/account_allocation/list.vue"),
        meta: { requiresAuth: true, children: true, parent: "/kidsenglish" },
    },
    {
        path: "/ke-contract",
        name: "ke-contract",
        component: () => import("../views/kidsenglish/contract/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/ex-warehouse",
        name: "ex-warehouse",
        component: () => import("../views/kidsenglish/ex-warehouse/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/ex-warehouse/add",
        name: "ex-warehouse.add",
        component: () => import("../views/kidsenglish/ex-warehouse/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/ex-warehouse/detail/:id",
        name: "ex-warehouse.detail",
        component: () => import("../views/kidsenglish/ex-warehouse/detail.vue"),
        meta: { requiresAuth: true },
    },
    //warehouse
    {
        path: "/warehouse",
        name: "warehouse.list",
        component: () => import("../views/kidsenglish/warehouse/list.vue"),
        meta: { requiresAuth: true },
    },
    // supplier
    {
        path: "/supplier",
        name: "supplier.list",
        component: () => import("../views/kidsenglish/supplier/list.vue"),
        meta: { requiresAuth: true },
    },
    //equipment type
    {
        path: "/equipment-type",
        name: "equipment_type.list",
        component: () =>
            import("../views/kidsenglish/equipment_types/list.vue"),
        meta: { requiresAuth: true },
    },
    //equipment
    {
        path: "/equipment",
        name: "equipment.list",
        component: () => import("../views/kidsenglish/equipments/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/equipment/import",
        name: "equipment.import",
        component: () => import("../views/kidsenglish/equipments/import.vue"),
        meta: { requiresAuth: true, children: true, parent: "/equipment" },
    },
    //business trip
    {
        path: "/business-trip",
        name: "business_trip.list",
        component: () => import("../views/business_trips/list.vue"),
        meta: { requiresAuth: true },
    },
    //hcns
    {
        path: "/folder",
        name: "folder.list",
        component: () => import("../views/folders/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/folder/:id",
        name: "folder.detail",
        component: () => import("../views/folders/detail.vue"),
        meta: { requiresAuth: true },
    },
    //properties
    {
        path: "/property-sub",
        name: "property_sub.list",
        component: () => import("../views/properties/property_sub.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/property-detail",
        name: "property_detail.list",
        component: () => import("../views/properties/property_detail.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/property-type",
        name: "property_type.list",
        component: () => import("../views/properties/property_list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/property",
        name: "property.list",
        component: () => import("../views/properties/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/sub-property/create",
        name: "sub_property_history.create",
        component: () => import("../views/properties/sub_property_history.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/sub-property-detail-edit/:id",
        name: "sub-property-detail.edit",
        component: () => import("../views/properties/sub_property_history.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/sub-property/:sub_property_id",
        name: "sub_property_history.list",
        component: () => import("../views/properties/sub_property_history.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/property/import-excel",
        name: "property.import",
        component: () => import("../views/properties/import.vue"),
        meta: { requiresAuth: true },
    },
    //kpi-upgrade
    {
        path: "/upgrade-register-kpi-list",
        name: "kpi-upgrade.list",
        component: () => import("../views/kpi-upgrade/register/list.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/upgrade-register-kpi/create",
        name: "kpi-upgrade.create",
        component: () =>
            import("../views/kpi-upgrade/register/register-form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/upgrade-register-kpi/:kpiSummaryId",
        name: "kpi-upgrade.detail",
        component: () => import("../views/kpi-upgrade/register/detail.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/upgrade-register-kpi/:kpiSummaryId/edit",
        name: "kpi-upgrade.edit",
        component: () =>
            import("../views/kpi-upgrade/register/register-form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/upgrade-kpi-assessment-list",
        name: "kpi-upgrade-assessment.list",
        component: () => import("../views/kpi-upgrade/assessment/list.vue"),
        meta: {
            requiresAuth: true,
        },
    },
    {
        path: "/upgrade-assessment-kpi/:kpiSummaryId",
        name: "kpi-upgrade-assessment.detail",
        component: () => import("../views/kpi-upgrade/assessment/detail.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/frequently-question-list",
        name: "questions.list",
        component: () => import("../views/questions/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/frequently-question-form",
        name: "questions.form",
        component: () => import("../views/questions/form.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/frequently-question-form/:id",
        name: "questions.form",
        component: () => import("../views/questions/form.vue"),
        meta: { requiresAuth: true },
    },
    // 404
    {
        path: "/404",
        name: "page404",
        component: () => import("../views/pages/error404.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "*",
        component: () => import("../views/pages/error404.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/app-display",
        name: "app-display.list",
        component: () => import("../views/app_displays/list.vue"),
        meta: { requiresAuth: true },
    },
    {
        path: "/test-indexeddb",
        name: "test-indexeddb",
        component: () => import("../views/test-indexeddb.vue"),
        meta: { requiresAuth: true },
    },
];

function getModuleComponent(moduleName, componentPath) {
    // This creates a path like: "../../modules/PartnerManagement/resources/js/views/list.vue"
    // Use webpackChunkName to help webpack understand the context better
    return () =>
        import(
            /* webpackChunkName: "module-[request]" */
            /* webpackInclude: /\.vue$/ */
            /* webpackExclude: /\.(php|sql|md|txt|log|xml|json)$/ */
            `../../../../modules/${moduleName}/resources/js/views/${componentPath}`
        );
}

const partnerRoutes = [
    {
        path: "/partners/login",
        name: "partner-login-form",
        component: getModuleComponent("PartnerManagement", "login_boxed.vue"),
        meta: { layout: "auth", requiresAuth: false },
    },
    {
        path: "/partners/dashboard",
        name: "partner-dashboard",
        component: getModuleComponent("PartnerManagement", "dashboard.vue"),
        meta: { layout: "partner", requiresAuth: true },
    },
    {
        path: "/partners/license/list/qlmn",
        name: "partner-license-list-qlmn",
        component: getModuleComponent("PartnerManagement", "license/qlmn/qlmn_list.vue"),
        meta: { layout: "partner", requiresAuth: true },
    },

    {
        path: "/partners/license/assign/qlmn",
        name: "partner-license-assign-qlmn",
        component: getModuleComponent("PartnerManagement", "license/qlmn/qlmn_assign.vue"),
        meta: { layout: "partner", requiresAuth: true },
    },
    {
        path: "/partners/notifications",
        name: "partner-notifications",
        component: () => import("../views/partners/notifications.vue"),
        meta: { layout: "partner", requiresAuth: true },
    },
];
routes.forEach(route => {
    if (!route.meta.layout) {
        route.meta.layout = "app";
    }
});

routes.push(...partnerRoutes);

const router = new VueRouter({
    mode: "history",
    linkExactActiveClass: "active",
    routes,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return { x: 0, y: 0 };
        }
    },
});

// router.beforeEach((to, from, next) => {
//     if (to.meta && to.meta.layout && to.meta.layout == 'auth') {
//         store.commit('setLayout', 'auth');
//     } else {
//         store.commit('setLayout', 'app');
//     }
//     next(true);
// });

export default router;
