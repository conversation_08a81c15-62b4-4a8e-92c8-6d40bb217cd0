<template>
    <!--  BEGIN SIDEBAR  -->
    <perfect-scrollbar
        class="list-unstyled menu-categories"
        tag="ul"
        :options="{
            wheelSpeed: 0.5,
            swipeEasing: !0,
            minScrollbarLength: 40,
            maxScrollbarLength: 300,
            suppressScrollX: true,
        }"
    >
        <li class="menu">
            <a
                href="javascript:void(0);"
                class="sidebarCollapse"
                data-placement="bottom"
                @click="
                    $store.commit(
                        'toggleSideBar',
                        !$store.state.is_show_sidebar
                    )
                "
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="feather feather-menu"
                >
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </a>
        </li>
        <!-- Trang chủ -->
        <router-link
            tag="li"
            :to="link_home"
            class="menu"
            @click.native="toggleMobileMenu"
        >
            <a class="dropdown-toggle">
                <div class>
                    <i data-feather="home"></i>
                    <span>{{ $t("dashboard") }}</span>
                </div>
            </a>
        </router-link>

        <!-- Quản lý dự án -->
        <router-link
            tag="li"
            to="/tasks-management/projects"
            class="menu"
            @click.native="toggleMobileMenu"
        >
            <a class="dropdown-toggle">
                <div class>
                    <i data-feather="layers"></i>
                    <span>Danh sách dự án</span>
                </div>
            </a>
        </router-link>
        <li
            v-if="!!$store.state.ticketSideBar.addUrl"
            class="menu cursor"
            :onclick="`${$store.state.ticketSideBar.addUrl}(true)`"
        >
            <a class="dropdown-toggle">
                <div class>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        class="feather feather-plus-circle"
                    >
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg>
                    <span>{{
                        $route.params.id ? "Tạo Ticket" : "Tạo dự án"
                    }}</span>
                </div>
            </a>
        </li>
    </perfect-scrollbar>
    <!--  END SIDEBAR  -->
</template>
<script>
import permission from "@/directive/permission/index.js";
import checkPermission from "@/helpers/permission";
import role from "@/directive/role/index.js";
import feather from "feather-icons";
import c from "@/helpers/common";

export default {
    directives: { permission, role },
    components: { feather },
    data() {
        return {
            menu_collapse: "dashboard",
            link_home: "/",
        };
    },
    watch: {
        $route(to) {
            let temp_path = to.meta.children ? to.meta.parent : to.path;
            // const selector = document.querySelector('#sidebar a[href="' + to.path + '"]');
            const selector = document.querySelector(
                '#sidebar a[href="' + temp_path + '"]'
            );
            if (selector) {
                const ul = selector.closest("ul.collapse");
                if (!ul) {
                    const ele = document.querySelector(
                        ".dropdown-toggle.not-collapsed"
                    );
                    if (ele) {
                        ele.click();
                    }
                }
            }
        },
    },
    created: function () {
        let permissions = c.session().user.permissions;
        if (
            (permissions.indexOf("dashboard_public") > -1 &&
                permissions.indexOf("dashboard_private") > -1) ||
            permissions.indexOf("dashboard_private") > -1
        ) {
            this.link_home = "/";
        } else {
            this.link_home = "/dashboard";
        }
    },
    mounted() {
        feather.replace();
        // default menu selection on refresh
        const selector = document.querySelector(
            '#sidebar a[href="' + window.location.pathname + '"]'
        );
        if (selector) {
            const ul = selector.closest("ul.collapse");
            if (ul) {
                let ele = ul
                    .closest("li.menu")
                    .querySelectorAll(".dropdown-toggle");
                if (ele) {
                    ele = ele[0];
                    setTimeout(() => {
                        ele.click();
                    });
                }
            } else {
                selector.click();
            }
        }
    },
    methods: {
        toggleMobileMenu() {
            if (window.innerWidth < 991) {
                this.$store.commit("toggleSideBar", true);
            }
        },
        hasPermission: function (permissions) {
            return checkPermission(permissions);
        },
    },
};
</script>
<style scoped>
#sidebar ul.menu-categories li.menu > .dropdown-toggle svg {
    margin-right: 5px !important;
}
</style>
