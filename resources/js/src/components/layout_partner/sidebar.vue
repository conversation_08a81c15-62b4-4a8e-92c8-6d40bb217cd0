<template>
    <!--  BEGIN SIDEBAR  -->
    <div class="sidebar-wrapper sidebar-theme">
        <nav ref="menu" id="sidebar">
            <div class="shadow-bottom"></div>

            <perfect-scrollbar class="list-unstyled menu-categories" tag="ul" :options="{
                wheelSpeed: 0.5,
                swipeEasing: !0,
                minScrollbarLength: 40,
                maxScrollbarLength: 300,
                suppressScrollX: true,
            }">
                <!-- Trang chủ -->
                <router-link tag="li" to="/partners/dashboard" class="menu" @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="home"></i>
                            <span>{{ $t("dashboard") }}</span>
                        </div>
                    </a>
                    
                </router-link>

                <li class="menu">
                    <a href="#components" class="dropdown-toggle">
                        <div class="">
                            <i data-feather="file-text"></i>
                            <span><PERSON><PERSON><PERSON><PERSON> phép đã cấp</span>
                        </div>

                    </a>
                    <div id="components" class="show">
                        <ul class="submenu list-unstyled">
                            <router-link tag="li" to="/partners/license/list/qlmn" @click.native="toggleMobileMenu"><a>Quản lý mầm non</a></router-link>
                            <!-- <router-link tag="li" to="/partners/license/list/qlcl" @click.native="toggleMobileMenu"><a>Kiểm định chất lượng</a></router-link> -->
                        </ul>
                    </div>
                </li>

                <!-- Quản lý Danh mục hệ thống -->
                <li class="menu">
                    <a href="#components" class="dropdown-toggle">
                        <div class="">
                            <i data-feather="user-plus"></i>
                            <span>Cấp tài khoản</span>
                        </div>
                    </a>
                    <div id="components" class="show">
                        <ul class="submenu list-unstyled">
                            <router-link tag="li" to="/partners/license/assign/qlmn" @click.native="toggleMobileMenu"><a>Quản lý mầm non</a></router-link>
                            <!-- <router-link tag="li" to="/partners/license/assign/qlcl" @click.native="toggleMobileMenu"><a>Kiểm định chất lượng</a></router-link> -->
                        </ul>
                    </div>
                </li>



            </perfect-scrollbar>
        </nav>
    </div>
    <!--  END SIDEBAR  -->
</template>
<script>
    import permission from "@/directive/permission/index.js";
    import checkPermission from "@/helpers/permission";
    import role from "@/directive/role/index.js";
    import feather from "feather-icons";
    import c from "@/helpers/common";
    import "@/views/style.css";

    export default {
        directives: { permission, role },
        components: { feather },
        data() {
            return {
                menu_collapse: "dashboard",
                link_home: "/",
            };
        },
        watch: {
            $route() {
                // No need to toggle menus as all submenus are always visible
            },
        },
        mounted() {
            feather.replace();
            // No need to toggle menus as all submenus are always visible
        },
        methods: {
            toggleMobileMenu() {
                if (window.innerWidth < 991) {
                    this.$store.commit("toggleSideBar", true);
                }
            },
            hasPermission: function (permissions) {
                return checkPermission(permissions);
            },
        },
        created() {
            let permissions = c.session().user.permissions;
            if (
                (permissions.indexOf("dashboard_public") > -1 &&
                    permissions.indexOf("dashboard_private") > -1) ||
                permissions.indexOf("dashboard_private") > -1
            ) {
                this.link_home = "/";
            } else {
                this.link_home = "/dashboard";
            }
        },
    };
</script>
<style scoped>
    #sidebar ul.menu-categories li.menu>.dropdown-toggle svg {
        margin-right: 5px !important;
    }
</style>
