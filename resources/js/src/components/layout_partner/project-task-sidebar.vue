<template>
    <!--  BEGIN SIDEBAR  -->
    <div
        class="sidebar-wrapper sidebar-theme d-block"
        v-if="$store.state.ticketSideBar.projectId.length > 0"
    >
        <nav ref="menu" id="sidebar">
            <div class="shadow-bottom"></div>
            <perfect-scrollbar
                class="list-unstyled menu-categories"
                tag="ul"
                :options="{
                    wheelSpeed: 0.5,
                    swipeEasing: !0,
                    minScrollbarLength: 40,
                    maxScrollbarLength: 300,
                    suppressScrollX: true,
                }"
            >
                <li class="project-name">
                    <div class="dropdown-toggle d-flex align-items-center">
                        <span
                            class="b-avatar icon-fill-info mr-2 badge-info rounded fs-18"
                            >{{ projectCode }}</span
                        >

                        <span
                            class="font-weight-bold span text-truncate"
                            :title="projectName"
                            >{{ projectName }}</span
                        >
                    </div>
                </li>
                <router-link
                    tag="li"
                    :to="`/tasks-management/projects/${$store.state.ticketSideBar.projectId}/tickets`"
                    class="menu"
                    @click.native="toggleMobileMenu"
                >
                    <a class="dropdown-toggle">
                        <div class>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="feather feather-bookmark"
                                data-v-5d511dd2=""
                            >
                                <path
                                    d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"
                                ></path>
                            </svg>
                            <span>DS nhiệm vụ</span>
                        </div>
                    </a>
                </router-link>
                <router-link
                    tag="li"
                    :to="`/tasks-management/projects/${$store.state.ticketSideBar.projectId}/config`"
                    class="menu"
                    @click.native="toggleMobileMenu"
                >
                    <a class="dropdown-toggle">
                        <div class>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="22"
                                height="22"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            >
                                <circle cx="12" cy="12" r="3"></circle>
                                <path
                                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
                                ></path>
                            </svg>
                            <span>Cấu hình dự án</span>
                        </div>
                    </a>
                </router-link>
                <router-link
                    tag="li"
                    :to="`/tasks-management/projects/${$store.state.ticketSideBar.projectId}/report`"
                    class="menu"
                    @click.native="toggleMobileMenu"
                >
                    <a class="dropdown-toggle">
                        <div class>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="22"
                                height="22"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            >
                                <circle cx="12" cy="12" r="3"></circle>
                                <path
                                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
                                ></path>
                            </svg>
                            <span>Báo cáo</span>
                        </div>
                    </a>
                </router-link>
            </perfect-scrollbar>
        </nav>
    </div>
    <!--  END SIDEBAR  -->
</template>
<script>
import permission from "@/directive/permission/index.js";
import checkPermission from "@/helpers/permission";
import role from "@/directive/role/index.js";
import feather from "feather-icons";
import c from "@/helpers/common";

export default {
    directives: { permission, role },
    components: { feather },
    data() {
        return {
            menu_collapse: "dashboard",
            link_home: "/",
        };
    },
    computed: {
        projectName() {
            return `${this.$store.state.ticketSideBar.projectCode} - ${this.$store.state.ticketSideBar.projectName}`;
        },
        projectCode() {
            const code = this.$store.state.ticketSideBar.projectCode;
            if (!code) return "N/A";
            return `${code.charAt(0)}${code.charAt(code.length - 1)}`;
        },
    },
    watch: {
        $route(to) {
            let temp_path = to.meta.children ? to.meta.parent : to.path;
            // const selector = document.querySelector('#sidebar a[href="' + to.path + '"]');
            const selector = document.querySelector(
                '#sidebar a[href="' + temp_path + '"]'
            );
            if (selector) {
                const ul = selector.closest("ul.collapse");
                if (!ul) {
                    const ele = document.querySelector(
                        ".dropdown-toggle.not-collapsed"
                    );
                    if (ele) {
                        ele.click();
                    }
                }
            }
        },
    },
    created: function () {
        let permissions = c.session().user.permissions;
        if (
            (permissions.indexOf("dashboard_public") > -1 &&
                permissions.indexOf("dashboard_private") > -1) ||
            permissions.indexOf("dashboard_private") > -1
        ) {
            this.link_home = "/";
        } else {
            this.link_home = "/dashboard";
        }
    },
    mounted() {
        feather.replace();
        // default menu selection on refresh
        const selector = document.querySelector(
            '#sidebar a[href="' + window.location.pathname + '"]'
        );
        if (selector) {
            const ul = selector.closest("ul.collapse");
            if (ul) {
                let ele = ul
                    .closest("li.menu")
                    .querySelectorAll(".dropdown-toggle");
                if (ele) {
                    ele = ele[0];
                    setTimeout(() => {
                        ele.click();
                    });
                }
            } else {
                selector.click();
            }
        }
    },
    methods: {
        toggleMobileMenu() {
            if (window.innerWidth < 991) {
                this.$store.commit("toggleSideBar", true);
            }
        },
        hasPermission: function (permissions) {
            return checkPermission(permissions);
        },
    },
};
</script>
<style scoped>
#sidebar ul.menu-categories li.menu > .dropdown-toggle svg {
    margin-right: 5px !important;
}
.fs-18 {
    font-size: 18px !important;
}
#sidebar ul.menu-categories li.project-name > .dropdown-toggle {
    margin-top: 21px;
    margin-bottom: 25px;
}
#sidebar ul.menu-categories li.menu > .dropdown-toggle,
.dropdown-toggle .span {
    color: #515365 !important;
}
</style>
