<template>
    <div>
        <!--  BEGIN NAVBAR  -->
        <div class="header-container fixed-top">
            <header class="header navbar navbar-expand-sm">
                <ul class="navbar-item theme-brand flex-row text-center">
                    <li class="nav-item theme-logo">
                        <router-link to="/partners/dashboard">
                            <img
                                src="/images/partner.png"
                                class="navbar-logo"
                                alt="logo"
                            />
                            <span class="text-light">VIETEC & ĐỐI TÁC</span>
                        </router-link>
                    </li>
                </ul>
                <div class="d-none horizontal-menu">
                    <a
                        href="javascript:void(0);"
                        class="sidebarCollapse"
                        data-placement="bottom"
                        @click="
                            $store.commit(
                                'toggleSideBar',
                                !$store.state.is_show_sidebar
                            )
                        "
                    >
                        <i data-feather="menu"></i>
                    </a>
                </div>

                <div class="navbar-item flex-row ml-md-auto">
                    <marquee scrollamount="5" v-show="marquee_webshare">
                        <i class="far fa-heart marquee-i"></i>
                        <i class="far fa-heart marquee-i"></i>
                        <i class="far fa-heart marquee-i"></i>
                        <span class="marquee-span">{{ marquee_webshare }}</span>
                        <i class="far fa-heart marquee-i"></i>
                        <i class="far fa-heart marquee-i"></i>
                        <i class="far fa-heart marquee-i"></i>
                    </marquee>

                    <b-dropdown
                        toggle-tag="a"
                        variant="icon-only"
                        toggle-class="nav-link"
                        menu-class="notification-scroll"
                        class="nav-item notification-dropdown"
                        :right="true"
                    >
                        <template #button-content>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="feather feather-bell"
                                data-v-8d2239c6=""
                            >
                                <path
                                    d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"
                                ></path>
                                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                            </svg>
                            <span
                                v-if="notifications.length != 0"
                                class="badge badge-success"
                                >{{ totalNotifications }}</span
                            >
                        </template>

                        <b-dropdown-item>
                            <b-media class="server-log">
                                <div class="data-info">
                                    <router-link to="/notifications"
                                        ><h4>
                                            <i class="fas fa-bell"></i> Thông
                                            báo
                                        </h4></router-link
                                    >
                                </div>
                            </b-media>
                        </b-dropdown-item>
                        <b-dropdown-divider></b-dropdown-divider>

                        <template v-if="notifications.length == 0">
                            <b-dropdown-item>
                                <b-media class="server-log">
                                    <div class="data-info">
                                        <h6 class="">Chưa có thông báo</h6>
                                    </div>
                                </b-media>
                            </b-dropdown-item>
                            <b-dropdown-divider></b-dropdown-divider>
                        </template>
                        <template v-else>
                            <b-dropdown-item>
                                <b-media class="server-log">
                                    <div class="data-info">
                                        <h6
                                            class="text-danger"
                                            @click="markAsReadAll()"
                                        >
                                            <i class="fas fa-check-double"></i>
                                            Đánh dấu tất cả đã đọc
                                        </h6>
                                    </div>
                                </b-media>
                            </b-dropdown-item>
                        </template>
                        <b-dropdown-divider></b-dropdown-divider>

                        <template v-for="(notify, i) in notifications">
                            <b-dropdown-item v-bind:key="notify.id">
                                <b-media class="server-log">
                                    <div class="data-info">
                                        <h6
                                            class="item-notify"
                                            @click="gotoLink(notify)"
                                        >
                                            <i
                                                class="fa-regular fa-comment"
                                            ></i>
                                            {{
                                                notify.title
                                                    ? notify.title
                                                    : notify.content
                                            }}
                                        </h6>
                                        <p class="item-notify-date">
                                            {{
                                                notify.created_at | formatDate4
                                            }}
                                        </p>
                                    </div>
                                    <div
                                        class="icon-status"
                                        @click="markAsRead(notify)"
                                        v-b-tooltip.top
                                        title="Đánh dấu là đã đọc"
                                    >
                                        <i class="fa-solid fa-check"></i>
                                    </div>
                                </b-media>
                            </b-dropdown-item>
                            <b-dropdown-divider
                                v-bind:key="i + 1"
                            ></b-dropdown-divider>
                        </template>
                    </b-dropdown>

                    <b-dropdown
                        toggle-tag="a"
                        variant="icon-only"
                        toggle-class="user nav-link"
                        class="nav-item user-profile-dropdown"
                        :right="true"
                    >
                        <template #button-content>
                            <img
                                src="/images/profile-11-80d0de0e88aaadff32fac5df9ac84ff6.jpeg"
                                alt="avatar"
                            />
                        </template>
                        <b-dropdown-item to="/users/profile">
                            <i data-feather="user"></i> {{ user_name }}
                        </b-dropdown-item>
                        <b-dropdown-divider></b-dropdown-divider>
                        <b-dropdown-item @click="change_password">
                            <i data-feather="lock"></i> Đổi mật khẩu
                        </b-dropdown-item>
                        <b-dropdown-item @click="logout">
                            <i data-feather="log-out"></i> Đăng xuất
                        </b-dropdown-item>
                    </b-dropdown>
                    <b-modal
                        title="Đổi mật khẩu"
                        hide-footer
                        v-model="change_password_modal"
                        centered
                    >
                        <b-overlay :show="overlay_model" rounded="center">
                            <div class="form mrl-20">
                                <div
                                    id="username-field"
                                    class="field-wrapper input"
                                >
                                    <div class="mrb-15">
                                        <div class="flex">
                                            <b-input
                                                placeholder="Mật khẩu cũ"
                                                :type="pwd_type"
                                                v-model="password.old_password"
                                            ></b-input>
                                            <svg
                                                @click="set_pwd_type"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                id="toggle-password"
                                                class="feather feather-eye show-password"
                                            >
                                                <path
                                                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                                                ></path>
                                                <circle
                                                    cx="12"
                                                    cy="12"
                                                    r="3"
                                                ></circle>
                                            </svg>
                                        </div>
                                        <span
                                            class="text-danger"
                                            v-show="errors.old_password"
                                            >{{ errors.old_password }}</span
                                        >
                                    </div>
                                    <div class="mrb-15">
                                        <div class="flex">
                                            <b-input
                                                :type="pwd_type"
                                                placeholder="Mật khẩu mới"
                                                v-model="password.new_password"
                                            ></b-input>
                                            <svg
                                                @click="set_pwd_type"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                id="toggle-password"
                                                class="feather feather-eye show-password"
                                            >
                                                <path
                                                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                                                ></path>
                                                <circle
                                                    cx="12"
                                                    cy="12"
                                                    r="3"
                                                ></circle>
                                            </svg>
                                        </div>
                                        <span
                                            class="text-danger"
                                            v-show="errors.new_password"
                                            >{{ errors.new_password }}</span
                                        >
                                    </div>
                                    <div class="mrb-15">
                                        <div class="flex">
                                            <b-input
                                                placeholder="Nhập lại mật khẩu mới"
                                                :type="pwd_type"
                                                v-model="
                                                    password.password_confirmation
                                                "
                                            ></b-input>
                                            <svg
                                                @click="set_pwd_type"
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                id="toggle-password"
                                                class="feather feather-eye show-password"
                                            >
                                                <path
                                                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                                                ></path>
                                                <circle
                                                    cx="12"
                                                    cy="12"
                                                    r="3"
                                                ></circle>
                                            </svg>
                                        </div>
                                        <span
                                            class="text-danger"
                                            v-show="
                                                errors.password_confirmation
                                            "
                                            >{{
                                                errors.password_confirmation
                                            }}</span
                                        >
                                    </div>
                                </div>
                                <b-button
                                    class="fr"
                                    variant="primary"
                                    @click="submit"
                                    >Đổi mật khẩu</b-button
                                >
                            </div>
                        </b-overlay>
                    </b-modal>
                </div>
            </header>
        </div>
        <div class="sub-header-container" v-if="!showTaskSideBar">
            <header class="header navbar navbar-expand-sm">
                <a
                    href="javascript:void(0);"
                    class="sidebarCollapse"
                    data-placement="bottom"
                    @click="
                        $store.commit(
                            'toggleSideBar',
                            !$store.state.is_show_sidebar
                        )
                    "
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        class="feather feather-menu"
                    >
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </a>

                <!-- Portal vue for Breadcrumb -->
                <portal-target name="breadcrumb"> </portal-target>
            </header>
        </div>
        <!--  END NAVBAR  -->
        <!--  BEGIN TOPBAR  -->
        <div class="topbar-nav header navbar" role="banner">
            <nav class="topbar">
                <task-sidebar v-if="showTaskSideBar" />
            </nav>
        </div>
    </div>
</template>
<script>
import { partner_logout, fetchDataAndSync } from "@/helpers/partner_auth";
import c from "@/helpers/common";
import "@/views/style.css";
import "@/assets/sass/font-icons/fontawesome/css/regular.css";
import "@/assets/sass/font-icons/fontawesome/css/fontawesome.css";
import feather from "feather-icons";
import TaskSidebar from "./task-sidebar.vue";
import NotificationDropdown from "./notification-dropdown.vue";
import Echo from "laravel-echo";
import Pusher from "pusher-js";
import storage from "@/helpers/storage";

export default {
    components: { TaskSidebar, NotificationDropdown },
    computed: {
        showTaskSideBar() {
            if (this.$route.name?.startsWith("tasks.")) return true;
            return false;
        },
    },
    data() {
        console.log(c.partner_session());
        return {
            avatar_image: "",
            user_id: c.partner_session().partner.id,
            pwd_type: "password",
            change_password_modal: false,
            overlay_model: false,
            password: {
                old_password: "",
                new_password: "",
                password_confirmation: "",
            },
            errors: {
                old_password: "",
                new_password: "",
                password_confirmation: "",
            },
            marquee_webshare: "",
            user_name: c.partner_session().partner.name,
            notifications: [],
            totalNotifications: 0,
            token: c.partner_token(),
            info: [],
        };
    },
    mounted() {
        feather.replace();
        this.listen();

        //this.fetchDataAndSync();
        // Optionally check every 60 seconds
        //this.intervalId = setInterval(this.fetchDataAndSync, 10000);
    },
    async created() {
        try {
            const notificationsData = await storage.getObject("notifications");
            this.notifications = notificationsData == "{}" || !notificationsData
                ? []
                : notificationsData;

            this.totalNotifications = Object.values(this.notifications).map(
                (item) => item
            ).length;

            console.log("TOTAL = " + this.totalNotifications);

            // this.info = await fetchDataAndSync();
        } catch (error) {
            console.error("Error loading notifications:", error);
            this.notifications = [];
            this.totalNotifications = 0;
        }
    },
    methods: {
        async logout() {
            const cfrm = confirm("Bạn có chắc là muốn thoát khỏi hệ thống?");
            if (cfrm == true) {
                await partner_logout();
                await storage.clear();
                await localStorage.clear();
                this.$router.push({ path: "/partners/login" });
            }
        },
        change_password() {
            this.change_password_modal = true;
        },
        set_pwd_type() {
            if (this.pwd_type == "password") {
                this.pwd_type = "text";
            } else {
                this.pwd_type = "password";
            }
        },
        submit() {
            c.p(`/api/changePassword`, this.password)
                .then((response) => {
                    this.$swal({
                        icon: "success",
                        title: "Đổi mật khẩu thành công",
                        confirmButtonText: "ok",
                        showLoaderOnConfirm: true,
                        closeOnClickOutside: false,
                        closeOnEsc: false,
                        allowOutsideClick: false,
                    });
                    this.change_password_modal = false;
                })
                .catch((e) => {
                    let errors = e.response.data.errors;
                    this.errors.old_password = errors.old_password;
                    this.errors.new_password = errors.new_password;
                    this.errors.password_confirmation =
                        errors.password_confirmation;
                });
        },
        gotoLink(item) {
            c.go(this.$router, item.data.link);
        },
        async getNotifications() {
            try {
                const response = await c.g(`/api/partners/partner/notifications?status=unread`);
                await storage.setObject("notifications", response);
                this.notifications = response;
                this.totalNotifications = this.notifications.length;
            } catch (error) {
                console.error("Error fetching notifications:", error);
            }
        },
        async markAsRead(item) {
            try {
                await c.p(`/api/partners/partner/notification/mark_as_read/` + item.id);
                //this.getNotifications();
                this.totalNotifications = this.totalNotifications - 1;
                this.notifications = this.notifications.filter(
                    (n) => n.id != item.id
                );
                await storage.setObject("notifications", this.notifications);
            } catch (error) {
                console.error("Error marking notification as read:", error);
            }
        },
        async markAsReadAll() {
            try {
                await c.p(`/api/partners/partner/notification/mark_as_read_all`);
                this.notifications = [];
                this.totalNotifications = 0;
                await storage.setObject("notifications", this.notifications);
                this.$store.commit("setSnackBar", {
                    type: "success",
                    msg: "Xử lý thành công!",
                    timeout: 3000,
                });
            } catch (error) {
                console.error("Error marking all notifications as read:", error);
            }
        },
        async listen() {
            // Pusher.logToConsole = process.env.NODE_ENV !== "production";

            if(this.token == null) return;

            window.Echo = new Echo({
                broadcaster: "pusher",
                wsHost: process.env.MIX_PUSHER_HOST,
                wsPort: process.env.MIX_PUSHER_PORT,
                key: process.env.MIX_PUSHER_APP_KEY,
                cluster: process.env.MIX_PUSHER_APP_CLUSTER,
                forceTLS: true,
                encrypted: false,
                authEndpoint: "/api/partners/pusher/check-auth",
                auth: {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        Accept: "application/json",
                    },
                },
            });

            if (this.totalNotifications == 0) {
                this.getNotifications();
            }

            // Listen for notifications on the private partner channel
            const channelName = "partner-" + c.md5(this.user_id.toString());
            // The event name should match the broadcastAs method in PartnerNotificationEvent
            const eventName = ".partner_channel_notifications";

            window.Echo.private(channelName).listen(
                eventName,
                (notification) => {
                    // Get notification data
                    const body = notification.message.body || "";
                    const title =
                        notification.message.type +
                            "| " +
                            notification.message.title || "Thông báo mới";

                    // Show a toast notification using Bootstrap Vue toast
                    this.$bvToast.toast(body, {
                        title: title,
                        headerClass: `toast-header-success`,
                        bodyClass: `toast-success`,
                        toaster: "b-toaster-bottom-left",
                        appendToast: true,
                        // autoHideDelay: 5000,
                        noAutoHide: true,
                        solid: true,
                    });

                    this.notifications = Object.values(this.notifications).map(
                        (item) => item
                    );
                    this.notifications = [
                        notification.message,
                        ...this.notifications,
                    ];
                    storage.setObject("notifications", this.notifications);

                    this.totalNotifications = this.notifications.length;
                    //this.$root.$emit("refresh-notifications");
                }
            );

            //this.getNotifications();
        },

        // async fetchDataAndSync() {
        //     try {
        //         this.info = await fetchDataAndSync();
        //     } catch (error) {
        //         console.error("Error fetching data:", error);
        //     }
        // },

        // beforeDestroy() {
        //     clearInterval(this.intervalId);
        // },
    },
};
</script>

<style scoped>
.navbar .navbar-item .nav-item.dropdown.notification-dropdown .dropdown-menu {
    min-width: 20rem !important;
}
.marquee-span {
    color: #3efb00 !important;
}
.marquee-i {
    color: red;
}
.navbar .navbar-item .nav-item.user-profile-dropdown .dropdown-menu {
    max-width: 15rem !important;
}
</style>
