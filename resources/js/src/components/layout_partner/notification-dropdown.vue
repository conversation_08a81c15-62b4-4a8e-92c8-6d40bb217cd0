<template>
  <li class="nav-item dropdown notification-dropdown">
    <a
      href="javascript:void(0);"
      class="nav-link dropdown-toggle"
      id="notificationDropdown"
      data-toggle="dropdown"
      aria-haspopup="true"
      aria-expanded="false"
    >
      <i data-feather="bell"></i>
      <span class="badge badge-success counter" v-if="unreadCount > 0">{{ unreadCount }}</span>
    </a>
    <div
      class="dropdown-menu position-absolute"
      aria-labelledby="notificationDropdown"
    >
      <div class="notification-scroll">
        <div class="dropdown-item d-flex justify-content-between">
          <div class="notification-title">
            <h6>Thông báo ({{ unreadCount }})</h6>
          </div>
          <div class="notification-action" v-if="unreadCount > 0">
            <a href="javascript:void(0)" @click="markAllAsRead">Đ<PERSON>h dấu đã đọc tất cả</a>
          </div>
        </div>

        <div v-if="loading" class="text-center p-3">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>

        <div v-else-if="notifications.length === 0" class="text-center p-3">
          <p class="mb-0">Không có thông báo mới</p>
        </div>

        <template v-else>
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="dropdown-item"
          >
            <div class="media" @click="markAsRead(notification)">
              <div class="notification-icon">
                <div class="icon-svg" :class="getIconClass(notification.data.type)">
                  <i :data-feather="getIcon(notification.data.type)"></i>
                </div>
              </div>
              <div class="media-body">
                <div class="notification-meta-time">
                  <h6>{{ notification.data.title }}</h6>
                  <p>{{ notification.data.body }}</p>
                  <span>{{ formatDate(notification.created_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>

        <div class="dropdown-item text-center">
          <router-link to="/partners/notifications" class="btn btn-primary btn-sm">
            Xem tất cả
          </router-link>
        </div>
      </div>
    </div>
  </li>
</template>

<script>
import c from "@/helpers/common";
import feather from "feather-icons";
import moment from "moment";

export default {
  data() {
    return {
      notifications: [],
      unreadCount: 0,
      loading: true
    };
  },
  mounted() {
    feather.replace();
    this.fetchNotifications();

    // Listen for refresh notifications event
    this.$root.$on('refresh-notifications', this.fetchNotifications);
  },

  beforeDestroy() {
    // Clean up event listener
    this.$root.$off('refresh-notifications', this.fetchNotifications);
  },
  methods: {
    async fetchNotifications() {
      this.loading = true;
      try {
        const response = await c.g('/api/partners/notifications/unread');
        if (response.success) {
          this.notifications = response.data;
          this.unreadCount = response.count;
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        this.loading = false;
        feather.replace();
      }
    },
    async markAsRead(notification) {
      try {
        await c.p(`/api/partners/notifications/${notification.id}/read`);
        this.fetchNotifications();
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    },
    async markAllAsRead() {
      try {
        await c.p('/api/partners/notifications/read-all');
        this.fetchNotifications();
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
      }
    },
    formatDate(date) {
      return moment(date).fromNow();
    },
    getIcon(type) {
      switch (type) {
        case 'license_activated':
          return 'key';
        case 'account_created':
          return 'user-plus';
        case 'license_expired':
          return 'alert-triangle';
        default:
          return 'bell';
      }
    },
    getIconClass(type) {
      switch (type) {
        case 'license_activated':
          return 'success';
        case 'account_created':
          return 'primary';
        case 'license_expired':
          return 'warning';
        default:
          return 'info';
      }
    }
  }
};
</script>

<style scoped>
.notification-scroll {
  max-height: 400px;
  overflow-y: auto;
}
.notification-icon {
  margin-right: 10px;
}
.icon-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.icon-svg.success {
  background-color: rgba(0, 150, 136, 0.1);
  color: #009688;
}
.icon-svg.primary {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}
.icon-svg.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}
.icon-svg.info {
  background-color: rgba(0, 188, 212, 0.1);
  color: #00bcd4;
}
.media {
  cursor: pointer;
}
.media:hover {
  background-color: #f8f9fa;
}
</style>
