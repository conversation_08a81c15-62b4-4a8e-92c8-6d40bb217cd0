import { partner_currentUser } from "../../helpers/partner_auth";
const user = partner_currentUser();

export default {
    state: {
        currentUser: user,
        isLoggedIn: false,
        loading: false,
        authError: null,
        isExpired: false,
        account_type: 'partner'
    },
    getters: {
        IS_LOADING: (state) => {
            return state.loading;
        },
        IS_LOGGEND_IN: (state) => {
            return (state.currentUser != null || state.currentUser != undefined);
        },
        CURRENT_USER: (state) => {
            return state.currentUser;
        },
        AUTH_ERROR: (state) => {
            return state.authError;
        },
        IS_EXPIRED: (state) => {
            return state.isExpired;
        },
    },
    mutations: {
        setIsExpired: (state, payload) => {
            state.isExpired = payload;
        },
        PARTNER_LOGIN: (state) => {
            state.loading = true;
            state.authError = null;
        },
        PARTNER_LOGIN_SUCCESS: (state, payload) => {
            state.isExpired = false;
            state.authError = null;
            state.isLoggedIn = true;
            state.loading = false;
            state.currentUser = Object.assign(
                {},
                payload.partner,
                { token: payload.access_token },
                { role: payload.role },
                { permissions: payload.permissions }
            );
            state.currentInfos = Object.assign({}, payload.infos);
            state.me = Object.assign({}, payload.me);
            state.notifications = Object.assign({}, payload.notifications);
            state.categories = Object.assign({}, payload.categories);

            localStorage.setItem("partner", JSON.stringify(state.currentUser));
            localStorage.setItem("categories", JSON.stringify(state.categories));
            localStorage.setItem("me", JSON.stringify(state.me));
            localStorage.setItem(
                "notifications",
                JSON.stringify(state.notifications)
            );
            localStorage.setItem("i18n_locale", "vi");
            localStorage.setItem("account_type", state.account_type);
        },
        PARTNER_LOGIN_FAILED: (state, payload) => {
            state.authError = false;
            state.loading = false;
        },
        PARTNER_LOGOUT: (state) => {
            localStorage.removeItem("partner");
            state.isLoggedIn = false;
            state.currentUser = null;
            state.account_type = null;
        },
    },
    actions: {
        PARTNER_LOGIN: (context) => {
            context.commit("PARTNER_LOGIN");
        },
    },
};
