Installation process.

The following process will be used to install admin dashboard with Laravel

1. Server Requirements: https://laravel.com/docs/8.x/deployment#server-requirements

2. Install php package manager Composer : Please install latest version of composer from https://getcomposer.org

3. This project is using Laravel version 8.x. https://laravel.com/docs/8.x

4. Install Node.js : Please install latest version of Node.js from https://nodejs.org

5. Copy laravel folder from themeforest bundle and extract to your suitable directory or folder.

6. Open terminal or command prompt with installation directory/folder.

7. Install PHP dependencies: composer install

8. Create new .env file from copying .env.example

9. Generate laravel app key php artisan key:generate

10. Install node dependencies: npm install

11. Run command npm run watch to start vue js development.

12. Run command npm run prod to build for production.

13. Run command php artisan serve to start php server which will run laravel or if you are using other server apps like WAMP, XAMP or MAMP, you can follow that guide.

14. Install Git : If you are a Git user. Please install latest version of Git from https://git-scm.com/

Note:- All the commands mentioned above can be executed using Git.

In case if you have any problems or query then please contact us

list queue

-   php artisan queue:work --queue=email
-   php artisan queue:work --queue=store_history_assessment_kpi

list cronjob

-   0 \* \* \* \* php /vietec/domains/internal.vietec.com.vn/public_html/artisan sync-callcenter

-   30 23 \* \* \* php /vietec/domains/internal.vietec.com.vn/public_html/artisan calculate-time-sheets now

-   0 1 1-2 \* \* php /vietec/domains/internal.vietec.com.vn/public_html/artisan calculate_absence_year

-   0 8 \* \* 1,2,3,4,5 php /vietec/domains/internal.vietec.com.vn/public_html/artisan calculate_timesheet_rd checkin

-   0 17 \* \* 1,2,3,4,5 php /vietec/domains/internal.vietec.com.vn/public_html/artisan calculate_timesheet_rd checkout

-   50 23 \* \* 1,2,3,4,5 php /vietec/domains/internal.vietec.com.vn/public_html/artisan in_out_general now

-   0 \* \* \* \* php /vietec/domains/internal.vietec.com.vn/public_html/artisan happy-birthday

Command:

php artisan queue:work --queue=qldn_tuanna_user_notifications

php artisan listener:notifications vietec

php artisan calculate_late_time (yêu cầu a tuanna chạy cho tôi vào sáng thứ 6 hàng tuần)

php artisan partner:create-company --name="Company Name" --short_name="CN" --prefix_code="COMP" --status=1

php artisan partner:generate-licenses 10 3

php artisan partner:assign-licenses --company_id=1 --count=5 --month=3

php artisan pms:create-account --province=123 --district=456 --ward=789 --school_name="Example School" --username="exampleschool" --school_level=1 --branches=2 --address="123 Example St" --phone="**********" --email="<EMAIL>" --projects=1,2,3


php artisan partner:update-expired-licenses