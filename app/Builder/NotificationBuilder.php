<?php

namespace App\Builder;

use App\Jobs\SendNotifyToAppJob;
use App\Jobs\SystemSendEmail;
use App\Jobs\SystemSendEmails;
use App\Jobs\SendEmailChangeLog;
use Monolog\Logger;

class NotificationBuilder
{
    const EMAIL = 'email';
    const MOBILE = 'mobile';
    const EVENT = 'event';
    const CHANGE_LOG = 'change_log';

    private static $obj;

    private final function __construct()
    {
    }

    public static function getBuilder()
    {
        if (!isset(self::$obj)) {
            self::$obj = new NotificationBuilder();
        }
        return self::$obj;
    }

    /***
     * @param NotificationConfig $config
     */
    public function sendMessage(NotificationConfig $config, $types = [self::MOBILE, self::EVENT])
    {
        if (in_array(self::MOBILE, $types)) {
            $this->sendToMobile($config);
        }
        if (in_array(self::EMAIL, $types)) {
            $this->sendEmail($config);
        }
        if (in_array(self::EVENT, $types)) {
            $this->sendEvent($config);
        }
        if (in_array(self::CHANGE_LOG, $types)) {
            $this->sendEmailChangeLog($config);
        }
    }

    private function sendToMobile(NotificationConfig $config)
    {

        try {
            $noti = $config->getNotification()();
            $config->getUser()->notify($noti);
            dispatch(new SendNotifyToAppJob($config->getUser(), $config->getMsg(), $config->getNotificationType()))->onQueue('notify_mobile');
        } catch (\Exception $e) {
            //Logger::toMonologLevel($e->getMessage());
        }
    }

    private function sendEmail(NotificationConfig $config)
    {
        try {
            $contentMails = $config->getContentMails();
            if (isset($contentMails) && is_array($contentMails) && !isArrEmptyOrNull($contentMails)) {
                $emailsJob = (new SystemSendEmails($contentMails))->onQueue('email');
                dispatch($emailsJob);
            }
        } catch (\Exception $e) {
            //Logger::toMonologLevel($e->getMessage());
        }
    }

    private function sendEvent(NotificationConfig $config)
    {
        try {
            event($config->getBroadcast()());
        } catch (\Exception $e) {
            //Logger::toMonologLevel($e->getMessage());
        }
    }

    private function sendEmailChangeLog(NotificationConfig $config){

        try {
            $contentMails = $config->getContentMails();
            if (isset($contentMails)) {
                $emailsJob = (new SendEmailChangeLog($contentMails))->onQueue('email');
                dispatch($emailsJob);
            }
        } catch (\Exception $e) {
            //Logger::toMonologLevel($e->getMessage());
        }
    }
}
