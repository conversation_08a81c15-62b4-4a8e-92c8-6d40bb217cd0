<?php

namespace App\Transformer;

use League\Fractal\TransformerAbstract;
use App\Models\Product;

class ProductTransformer extends TransformerAbstract
{
    public function transform(Product $product)
    {
        return [
            'id' => $product->id,
            'name' => $product->name,
            'product_category_id' => $product->product_category_id,
            'code' => $product->code,
            'short_name' => $product->short_name,
            'link_recommend' => $product->link_recommend,
            'leader' => $product->leader,
            'type' => $product->type,
            'available_partner' => $product->available_partner,
            'show' => $product->show,
            // Add more fields as needed
        ];
    }
}
