<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Carbon\Carbon;

class JsonDateCast implements CastsAttributes
{
    protected $dateFields;

    public function __construct(array $dateFields = ['some_date'])
    {
        $this->dateFields = $dateFields;
    }

    /**
     * Cast the given value.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return array
     */
    public function get($model, string $key, $value, array $attributes)
    {
        $data = is_array($value) ? $value : json_decode($value, true) ?? [];

        foreach ($this->dateFields as $field) {
            if (isset($data[$field])) {
                if (is_numeric($data[$field])) {
                    $data[$field] = Carbon::createFromTimestampMs($data[$field])
                        ->setTimezone('Asia/Ho_Chi_Minh')
                        ->format('Y-m-d H:i:s');
                } else {
                    $data[$field] = Carbon::parse($data[$field])
                        ->setTimezone('Asia/Ho_Chi_Minh')
                        ->format('Y-m-d H:i:s');
                }
            }
        }

        return $data;
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  string  $key
     * @param  mixed  $value
     * @param  array  $attributes
     * @return array
     */
    public function set($model, string $key, $value, array $attributes)
    {
        $data = is_array($value) ? $value : json_decode($value, true) ?? [];

        foreach ($this->dateFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = Carbon::parse($data[$field])->getTimestampMs();
            }
        }

        return $data;
    }
}