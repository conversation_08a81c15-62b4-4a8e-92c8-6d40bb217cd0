<?php

namespace App\Models;

use Spatie\Activitylog\Contracts\Activity as ActivityContract;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Model as EloquentModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use DateTimeInterface;

class MongoActivity extends MongoModel implements ActivityContract
{
    protected $connection = 'mongodb';
    protected $collection = 'activity_log';
    protected $guarded = [];
    
    protected $casts = [
        'properties'     => 'array',
        'old_properties' => 'array',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime'
    ];

    public $incrementing = false;
    protected $primaryKey = '_id';
    protected $keyType = 'string';

    /**
     * Format dates for serialization.
     *
     * @param  \DateTimeInterface  $date
     * @return string
     */
    // protected function serializeDate(DateTimeInterface $date): string
    // {
    //     return Carbon::createFromTimestampMs($date->getTimestamp())
    //         ->setTimezone('Asia/Ho_Chi_Minh')
    //         ->format('Y-m-d H:i:s');
    // }

    // Spatie Activitylog Contract Methods (unchanged)
    public function subject(): MorphTo
    {
        if (config('activitylog.subject_model')) {
            return $this->morphTo('subject', config('activitylog.subject_model'));
        }
        return $this->morphTo();
    }

    public function causer(): MorphTo
    {
        if (config('activitylog.causer_model')) {
            return $this->morphTo('causer', config('activitylog.causer_model'));
        }
        return $this->morphTo();
    }

    public function getExtraProperty(string $propertyName, mixed $defaultValue = null): mixed
    {
        return Arr::get($this->properties->toArray(), $propertyName);
    }

    public function changes(): Collection
    {
        if (! $this->properties instanceof Collection) {
            $this->properties = collect($this->properties);
        }
        if (! $this->old_properties instanceof Collection) {
            $this->old_properties = collect($this->old_properties);
        }
        return $this->properties->diff($this->old_properties);
    }

    public function scopeInLog(Builder $query, ...$logName): Builder
    {
        return $query->where('log_name', $logName);
    }

    public function scopeCausedBy(Builder $query, EloquentModel $causer): Builder
    {
        return $query
            ->where('causer_type', $causer->getMorphClass())
            ->where('causer_id', $causer->getKey());
    }

    public function scopeForEvent(Builder $query, string $event): Builder
    {
        return $query->where('event', $event);
    }

    public function scopeForSubject(Builder $query, EloquentModel $subject): Builder
    {
        return $query
            ->where('subject_type', $subject->getMorphClass())
            ->where('subject_id', $subject->getKey());
    }
}