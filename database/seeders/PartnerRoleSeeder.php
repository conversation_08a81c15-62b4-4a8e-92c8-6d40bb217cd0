<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;

class PartnerRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create partner roles with partner-api guard
        $roles = [
            'partner-admin' => 'Quản trị đối tác',
            'partner-user' => 'Người dùng đối tác',
        ];

        foreach ($roles as $name => $title) {
            Role::create([
                'name' => $name,
                'title' => $title,
                'guard_name' => 'partner-api',
            ]);
        }

        // Assign permissions to roles
        $adminRole = Role::where('name', 'partner-admin')->where('guard_name', 'partner-api')->first();
        $userRole = Role::where('name', 'partner-user')->where('guard_name', 'partner-api')->first();

        // Get partner permissions
        $permissions = Permission::where('name', 'like', 'partner_%')->get();

        // Assign all partner permissions to partner-admin role
        $adminRole->syncPermissions($permissions);

        // Assign only view permissions to partner-user role
        $viewPermissions = Permission::where('name', 'like', 'partner_view%')
            ->orWhere('name', 'like', 'partner_list%')
            ->get();
        $userRole->syncPermissions($viewPermissions);
    }
}
