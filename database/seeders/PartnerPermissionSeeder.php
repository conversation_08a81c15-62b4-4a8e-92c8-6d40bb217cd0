<?php

namespace Database\Seeders;

use App\Models\Module;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PartnerPermissionSeeder extends Seeder
{
    public function run()
    {
        // Create module
        $module = Module::create([
            'name' => 'partner',
            'title' => 'Quản lý đối tác',
        ]);

        // Create permissions
        $permissions = [
            'partner_list' => 'Xem danh sách đối tác',
            'partner_view' => 'Xem chi tiết đối tác',
            'partner_add' => 'Thêm đối tác',
            'partner_edit' => 'Sửa đối tác',
            'partner_delete' => 'Xóa đối tác',
        ];

        foreach ($permissions as $name => $title) {
            Permission::create([
                'name' => $name,
                'title' => $title,
                'module_id' => $module->id,
                'guard_name' => 'partner-api',
            ]);
        }
    }
}
