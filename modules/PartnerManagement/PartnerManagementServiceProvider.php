<?php

namespace Modules\PartnerManagement;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Routing\Router;
use Modules\PartnerManagement\Models\Partner;
use Modules\PartnerManagement\Policies\PartnerPolicy;
use Modules\PartnerManagement\Http\Middleware\PartnerRoleMiddleware;
use Modules\PartnerManagement\Http\Middleware\PartnerPermissionMiddleware;
use Modules\PartnerManagement\Console\Commands\CreatePartnerCompanyCommand;
use Modules\PartnerManagement\Console\Commands\GeneratePartnerLicensesCommand;
use Modules\PartnerManagement\Console\Commands\AssignLicensesToCompanyCommand;
use Modules\PartnerManagement\Console\Commands\PMSInfoCommand;
use Modules\PartnerManagement\Console\Commands\CreatePMSAccountCommand;
use Modules\PartnerManagement\Console\Commands\UpdateExpiredLicensesCommand;

class PartnerManagementServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                CreatePartnerCompanyCommand::class,
                GeneratePartnerLicensesCommand::class,
                AssignLicensesToCompanyCommand::class,
                PMSInfoCommand::class,
                CreatePMSAccountCommand::class,
                UpdateExpiredLicensesCommand::class,
            ]);
        }
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Load routes
        $this->loadRoutesFrom(__DIR__ . '/routes.php');

        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/database/migrations');

        // Register policies
        Gate::policy(Partner::class, PartnerPolicy::class);

        // Register middlewares
        $router = $this->app->make(Router::class);
        $router->aliasMiddleware('partner.role', PartnerRoleMiddleware::class);
        $router->aliasMiddleware('partner.permission', PartnerPermissionMiddleware::class);
    }
}
