<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Modules\PartnerManagement\Models\Partner;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Response;
use Modules\PartnerManagement\Transformers\PartnerNotificationTransformer;
use Modules\PartnerManagement\Transformers\ResponseTransformer;

class PartnerController extends Controller
{
    public function index(Request $request)
    {
        //$this->authorize('viewAny', Partner::class);

        $query = Partner::query();

        // Apply filters
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('code', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%")
                    ->orWhere('phone', 'like', "%{$keyword}%");
            });
        }

        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }

        // Pagination
        $perPage = $request->per_page ?? 10;
        $partners = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json($partners);
    }

    public function store(Request $request)
    {
        $this->authorize('create', Partner::class);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:partners',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $partner = Partner::create($request->all());
        return response()->json($partner, 201);
    }

    public function show(Partner $partner)
    {
        $this->authorize('view', $partner);
        return response()->json($partner);
    }

    public function update(Request $request, Partner $partner)
    {
        $this->authorize('update', $partner);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:partners,code,' . $partner->id,
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $partner->update($request->all());
        return response()->json($partner);
    }

    public function destroy(Partner $partner)
    {
        $this->authorize('delete', $partner);
        $partner->delete();
        return response()->json(['message' => 'Partner deleted successfully']);
    }

    /**
     * Notification
     */
    function list_notifications(Request $request)
    {
        $partner = auth('partner-api')->user();

        $query = $partner->notifications();

        // Filter by status (read/unread/all)
        if ($request->filled('status')) {
            if ($request->status === 'read') {
                $query->whereNotNull('read_at');
            } elseif ($request->status === 'unread') {
                $query->whereNull('read_at');
            }
        }

        $notifications = $query->orderBy('created_at', 'desc')->get();

        return fractal()
            ->collection($notifications)
            ->transformWith(new PartnerNotificationTransformer())
            ->respond(config('apicode.SUCCESS'));
    }

    /**
     * Mark as read
     */
    public function mark_as_read(Request $request)
    {
        try {
            $id = $request->id;
            $partner = auth('partner-api')->user();
            $notification = $partner->notifications()->where('id', $id)->whereNull('read_at')->first();

            if (!$notification) {
                return api_response(false, 'Không tồn tại thông báo !');
            }

            $notification->markAsRead();

            return api_response(
                true,
                'Đã đọc thông báo !',
                $notification,
                200,
                null,
                PartnerNotificationTransformer::class
            );
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }

    /**
     * Mark as read all
     */
    public function mark_as_read_all(Request $request)
    {
        try {
            $partner = auth('partner-api')->user();
            $partner->unreadNotifications->markAsRead();

            return api_response(
                true,
                'Đã đọc tất cả thông báo !'
            );
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }
}
