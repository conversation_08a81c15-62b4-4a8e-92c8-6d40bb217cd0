<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Notifications\DatabaseNotification;
use Modules\PartnerManagement\Models\Partner;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;
use Modules\PartnerManagement\Notifications\PartnerNotification;

class PartnerNotificationController extends Controller
{
    /**
     * Get all notifications for the authenticated partner
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $partner = Auth::guard('partner-api')->user();
        $notifications = $partner->notifications()->paginate(20);
        
        return response()->json([
            'success' => true,
            'data' => $notifications
        ]);
    }

    /**
     * Get unread notifications for the authenticated partner
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unread(Request $request)
    {
        $partner = Auth::guard('partner-api')->user();
        $notifications = $partner->unreadNotifications;
        
        return response()->json([
            'success' => true,
            'data' => $notifications,
            'count' => $notifications->count()
        ]);
    }

    /**
     * Mark a notification as read
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, $id)
    {
        $partner = Auth::guard('partner-api')->user();
        $notification = $partner->notifications()->where('id', $id)->first();
        
        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }
        
        $notification->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    /**
     * Mark all notifications as read
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        $partner = Auth::guard('partner-api')->user();
        $partner->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Send a notification to a partner
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function send(Request $request)
    {
        $request->validate([
            'partner_id' => 'required|exists:partners,id',
            'title' => 'required|string',
            'body' => 'required|string',
            'type' => 'required|string',
            'data' => 'nullable|array',
            'option' => 'nullable|string|in:add,update,delete'
        ]);
        
        $partner = Partner::findOrFail($request->partner_id);
        $option = $request->option ?? 'add';
        
        $partner->sendPusherNotification(
            $request->title,
            $request->body,
            $request->type,
            $request->data ?? [],
            $option
        );
        
        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully'
        ]);
    }
}
