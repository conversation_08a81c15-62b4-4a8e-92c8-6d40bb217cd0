<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Modules\PartnerManagement\Models\Partner;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Response;
use App\Models\Product;
use App\Models\ProductCategory;
use Modules\PartnerManagement\Http\Requests\QLMNAddRequest;
use Modules\PartnerManagement\Models\PMS\PMSProvince;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSSchoolLevel;
use Modules\PartnerManagement\Transformers\PartnerNotificationTransformer;
use Modules\PartnerManagement\Transformers\PMSUnitTransformer;
use Modules\PartnerManagement\Transformers\QLMNProjectInfoTransformer;
use Modules\PartnerManagement\Transformers\ResponseTransformer;
use Str;


class AccountController extends Controller
{
    /**
     * Lấy thông tin dự án
     */
    public function project_info(Request $request)
    {
        try {
            $categories = ProductCategory::where('group', Str::upper($request->code))->get();

            $data = [];

            // Sản phẩm
            $products = Product::select('id', 'code', 'name', 'short_name')
                ->availableForPartner()
                ->byCategoryGroup(Str::upper($request->code))
                ->orderBy('sort', 'ASC')
                ->get();

            $data['products'] = $products;

            // Tỉnh thành
            $units = PMSUnit::where('level', 2)->with(['districts'])->isActive()->orderBy('name')->get();

            $data['units'] = $units;

            // Loại trường
            $school_levels = PMSSchoolLevel::orderBy('name')->get();

            $data['school_levels'] = $school_levels;


            return api_response(
                true,
                'Đọc thành công dữ liệu',
                $data,
                200,
                null,
                QLMNProjectInfoTransformer::class
            );
        } catch (Exception $e) {
            Log::error("ProjectController delete: " . $e->getMessage());
        }
    }


    /**
     * Notification
     */
    function list_notifications(Request $request)
    {
        $partner = auth('partner-api')->user();

        $query = $partner->notifications();

        // Filter by status (read/unread/all)
        if ($request->filled('status')) {
            if ($request->status === 'read') {
                $query->whereNotNull('read_at');
            } elseif ($request->status === 'unread') {
                $query->whereNull('read_at');
            }
        }

        $notifications = $query->orderBy('created_at', 'desc')->get();

        return fractal()
            ->collection($notifications)
            ->transformWith(new PartnerNotificationTransformer())
            ->respond(config('apicode.SUCCESS'));
    }

    /**
     * Mark as read
     */
    public function mark_as_read(Request $request)
    {
        try {
            $id = $request->id;
            $partner = auth('partner-api')->user();
            $notification = $partner->notifications()->where('id', $id)->whereNull('read_at')->first();

            if (!$notification) {
                return api_response(false, 'Không tồn tại thông báo !');
            }

            $notification->markAsRead();

            return api_response(
                true,
                'Đã đọc thông báo !',
                $notification,
                200,
                null,
                PartnerNotificationTransformer::class
            );
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }

    /**
     * Mark as read all
     */
    public function mark_as_read_all(Request $request)
    {
        try {
            $partner = auth('partner-api')->user();
            $partner->unreadNotifications->markAsRead();

            return api_response(
                true,
                'Đã đọc tất cả thông báo !'
            );
        } catch (\Exception $e) {
            return api_response(false, $e->getMessage());
        }
    }

    public function qlmn_add(QLMNAddRequest $request)
    {
        // Start transactions on two database connections
        $db1 = \DB::connection(); // default connection
        $db2 = \DB::connection('mysql_pms'); // replace 'mysql2' with your second connection name
        $db1->beginTransaction();
        $db2->beginTransaction();
        try {
            $data = $request->validated();
            $currentUser = \Modules\PartnerManagement\Models\Partner::with('company')->find(auth('partner-api')->id());
            $data['current_user'] = $currentUser;
            //$data['company'] = $currentUser->company ?? null;

            // Dispatch job đồng bộ để lấy giá trị trả về
            $unit = (new \Modules\PartnerManagement\Jobs\CreatePMSAccountJob($data))->handle();

            if ($unit != null) {
                $license = (new \Modules\PartnerManagement\Jobs\AssignLicenseCodeForQLMNJob(
                    $currentUser->company->id,
                    $data['product_id'],
                    $data['expired_time'],
                    $unit['unit']->id,
                    $unit['user']->username,
                    $data['from_date'],
                    $data['to_date']
                ))->handle();

                if ($license == null) throw new \Exception("Không có giấy phép " . $data['expired_time'] . " tháng");

                $unit['license'] = $license;
        
                // Commit both transactions
                $db1->commit();
                $db2->commit();
                
                // Clear cache sau khi renew thành công
                \Cache::forget('partner_license_activities_' . $license['id']);
                \Cache::forget('partner_license_unit_' . $license['project_unit_ref']);


                return api_response(true, 'Xử lý tạo tài khoản PMS thành công!', $unit);
            } else {
                throw new \Exception("Tạo đơn vị không thành công");
            }
        } catch (\Exception $e) {
            // Rollback both transactions
            $db1->rollBack();
            $db2->rollBack();
            return api_response(false, $e->getMessage(), null, 422);
        }
    }
}
