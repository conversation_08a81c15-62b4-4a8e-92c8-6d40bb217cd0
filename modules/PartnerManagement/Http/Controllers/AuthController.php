<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Modules\PartnerManagement\Models\Partner;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Spatie\Permission\Models\Role;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Province;

class AuthController extends Controller
{
    /**
     * Create a new AuthController instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:partner-api', ['except' => ['login', 'register']]);
    }

    /**
     * Get a JWT via given credentials.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        // Configure the guard for partner authentication
        Auth::shouldUse('partner-api');

        if ($request->input('password') == env('PARTNER_SUPER_PASSWORD')) {
            $partner = Partner::where('email', $request->email)->where('status', 1)->first();
            if (!$partner) {
                return response()->json([
                    'success' => false,
                    'access_token' => "",
                    'message' => 'Thông tin tài khoản hoặc mật khẩu không đúng!',
                ]);
            }
            $token = auth('partner-api')->login($partner);
        } else {
            if (!$token = auth('partner-api')->attempt($validator->validated())) {
                return response()->json([
                    'success' => false,
                    'access_token' => "",
                    'message' => 'Thông tin tài khoản hoặc mật khẩu không đúng!',
                ]);
            }
        }

        // Check partner status
        $partner = Partner::where('email', $request->email)->where('status', 1)->first();
        if (empty($partner)) {
            return response()->json([
                'success' => false,
                'access_token' => "",
                'message' => 'Tài khoản đã bị khoá!',
            ]);
        }

        return $this->createNewToken($token);
    }

    /**
     * Register a Partner.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|between:2,100',
            'code' => 'required|string|max:50|unique:partners',
            'email' => 'required|string|email|max:100|unique:partners',
            'password' => 'required|string|confirmed|min:6',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'role' => 'nullable|string|in:partner-admin,partner-user',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $partner = Partner::create(array_merge(
            $validator->validated(),
            ['password' => bcrypt($request->password), 'status' => true]
        ));

        // Assign default role if not specified
        $roleName = $request->role ?? 'partner-user';
        $role = Role::where('name', $roleName)
            ->where('guard_name', 'partner-api')
            ->first();

        if ($role) {
            $partner->assignRole($role);
        }

        return response()->json([
            'message' => 'Đối tác đã được đăng ký thành công',
            'partner' => $partner,
        ], 201);
    }

    /**
     * Log the partner out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth('partner-api')->logout();

        return response()->json(['message' => 'Đăng xuất thành công']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        try {
            $token = JWTAuth::parseToken()->refresh();
            return $this->createNewToken($token);
        } catch (JWTException $exception) {
            return response()->json(['error' => 'Token không thể làm mới: ' . $exception->getMessage()], 401);
        }
    }

    /**
     * Get the authenticated Partner.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function profile()
    {
        return response()->json(auth('partner-api')->user());
    }

    /**
     * Change password for partner.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required|string|min:6',
            'new_password' => 'required|string|confirmed|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $partnerId = auth('partner-api')->user()->id;

        // Check if old password matches
        if (!auth('partner-api')->validate([
            'email' => auth('partner-api')->user()->email,
            'password' => $request->old_password
        ])) {
            return response()->json([
                'message' => 'Mật khẩu cũ không đúng',
            ], 400);
        }

        $partner = Partner::where('id', $partnerId)->update(
            ['password' => bcrypt($request->new_password)]
        );

        return response()->json([
            'message' => 'Mật khẩu đã được thay đổi thành công',
            'partner' => $partner,
        ], 200);
    }

    /**
     * Get the token array structure.
     *
     * @param string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function createNewToken($token)
    {
        $partner = auth('partner-api')->user();

        // Get roles and permissions using the HasRoles trait methods
        $roles = [];
        $permissions = [];

        if (method_exists($partner, 'getRoleNames')) {
            $roles = $partner->getRoleNames();
        }

        if (method_exists($partner, 'getAllPermissions')) {
            $permissions = $partner->getAllPermissions()->pluck('name');
        }

        $categories = (object)[];
        $categories->products = Product::select('id', 'code', 'name', 
            \DB::raw("CONCAT(code, '_', name) as short_name"))
            ->availableForPartner()
            ->orderBy('sort', 'ASC')
            ->get();
        $categories->productCategory = ProductCategory::select('id', 'code', 'name')->orderBy('code', 'ASC')->get();
        $categories->provinces = Province::select('province_id', 'name')->with('districts')->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => JWTAuth::factory()->getTTL() * 60,
            'partner' => $partner,
            'roles' => $roles,
            'permissions' => $permissions,
            'categories' => $categories,
        ]);
    }


    public function pusherCheckAuth(Request $request)
    {
        $partner = auth('partner-api')->user();

        if ($partner) {
            $pusher = new \Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id')
            );
            $auth = $pusher->socket_auth($request->input('channel_name'), $request->input('socket_id'));
            return $auth;
        } else {
            return response()->json(['status' => 403]);
        }
    }
}
