<?php

namespace Modules\PartnerManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;

class QLMNAddRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'province_id' => 'required|exists:mysql_pms.units,id,level,2,status,1',
            'district_id' => [
                'required',
                function ($attribute, $value, $fail) {
                    $provinceId = $this->input('province_id');
                    $district = PMSUnit::where('id', $value)->isActive()->where('level', 3)->where('parent_id', $provinceId)->first();
                    if (!$district) {
                        $fail('Không tồn tại quận/huyện mã #' . $value);
                    }
                },
            ],
            'ward_id' => [
                'required',
                function ($attribute, $value, $fail) {
                    $districtId = PMSUnit::where('id', $this->input('district_id'))->isActive()->where('level', 3)->value('district');
                    //dd($districtId);
                    $ward = PMSWard::where('id', $value)->where('district', $districtId)->first();
                    if (!$ward) {
                        $fail('Không tồn tại phường/xã mã #' . $value);
                    }
                },
            ],
            'school_name' => 'required|string|max:255',
            'school_level' => 'required|integer|exists:mysql_pms.school_levels,id',
            'school_point' => 'nullable|numeric|min:1',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:250',
            'product_id' => 'required|integer|exists:m_products,id,available_partner,1',
            'account' => [
                'required',
                'string',
                'max:100',
                function ($attribute, $value, $fail) {
                    $exists = PMSUser::where('username', $value)->first();
                    if ($exists) {
                        $fail('Tài khoản ' . $value . ' đã tồn tại trong hệ thống.');
                    }
                },
            ],
            'expired_time' => 'required|integer|in:1,3,6,12,24,36',
            'from_date' => 'nullable|date|after_or_equal:today',
            'to_date' => 'nullable|date|after_or_equal:from_date',
        ];
    }

    public function attributes()
    {
        return [
            'district_id' => 'Quận/Huyện',
            'ward_id' => 'Phường/Xã',
            'school_name' => 'Tên trường',
            'school_level' => 'Cấp trường',
            'school_point' => 'Điểm trường',
            'email' => 'Email',
            'phone' => 'Số điện thoại',
            'product_id' => 'Sản phẩm',
            'account' => 'Tài khoản',
            'expired_time' => 'Ngày hết hạn',
            'from_date' => 'Từ ngày',
            'to_date' => 'Đến ngày',
        ];
    }
}
