<?php

namespace Modules\PartnerManagement\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PartnerRoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $role)
    {
        if (Auth::guard('partner-api')->guest()) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        $roles = is_array($role)
            ? $role
            : explode('|', $role);

        $partner = Auth::guard('partner-api')->user();
        
        // Check if the partner has the required role
        if (method_exists($partner, 'hasAnyRole')) {
            if (!$partner->hasAnyRole($roles)) {
                return response()->json(['error' => 'Unauthorized. You do not have the required role.'], 403);
            }
        } else {
            // Fallback if the HasRoles trait is not properly set up
            return response()->json(['error' => 'Role checking is not available.'], 500);
        }

        return $next($request);
    }
}
