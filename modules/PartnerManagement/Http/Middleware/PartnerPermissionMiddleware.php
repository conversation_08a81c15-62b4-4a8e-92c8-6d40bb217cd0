<?php

namespace Modules\PartnerManagement\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PartnerPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        if (Auth::guard('partner-api')->guest()) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        $permissions = is_array($permission)
            ? $permission
            : explode('|', $permission);

        $partner = Auth::guard('partner-api')->user();
        
        // Check if the partner has the required permission
        if (method_exists($partner, 'hasAnyPermission')) {
            if (!$partner->hasAnyPermission($permissions)) {
                return response()->json(['error' => 'Unauthorized. You do not have the required permissions.'], 403);
            }
        } else {
            // Fallback if the HasRoles trait is not properly set up
            return response()->json(['error' => 'Permission checking is not available.'], 500);
        }

        return $next($request);
    }
}
