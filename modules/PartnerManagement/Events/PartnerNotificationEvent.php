<?php

namespace Modules\PartnerManagement\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\PartnerManagement\Models\Partner;

class PartnerNotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $partner, $message;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Partner $partner, $message)
    {
        $this->partner = $partner;
        $this->message = $message;
    }

    /**
     * Get the PrivateChannel the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\PrivateChannel|array
     */
    public function broadcastOn()
    {
        //dd('private-partner-' . md5($this->partner->id));
        dump('📡 Event sẽ được broadcast trên channel: chat');
        return new PrivateChannel('partner-' . md5($this->partner->id));
    }

    public function broadcastAs()
    {
        return 'partner_channel_notifications';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith()
    {
        return [
            'id' => $this->partner->id,
            'name' => $this->partner->name,
            'message' => $this->message
        ];
    }
}
