<template>
    <div>
        <!--  BEGIN NAVBAR  -->
        <Header></Header>
        <!--  END NAVBAR  -->

        <!--  BEGIN MAIN CONTAINER  -->
        <div
            class="main-container"
            id="container"
            :class="[
                !$store.state.is_show_sidebar ? 'sidebar-closed sbar-open' : '',
                $store.state.menu_style === 'collapsible-vertical'
                    ? 'collapsible-vertical-mobile'
                    : '',
            ]"
        >
            <!--  BEGIN OVERLAY  -->
            <div
                class="overlay"
                :class="{ show: !$store.state.is_show_sidebar }"
                @click="
                    $store.commit(
                        'toggleSideBar',
                        !$store.state.is_show_sidebar
                    )
                "
            ></div>
            <div
                class="search-overlay"
                :class="{ show: $store.state.is_show_search }"
                @click="
                    $store.commit('toggleSearch', !$store.state.is_show_search)
                "
            ></div>
            <!-- END OVERLAY -->

            <!--  BEGIN SIDEBAR  -->
            <template v-if="notTasksSideBar">
                <Sidebar></Sidebar>
            </template>
            <template v-else>
                <ProjectTaskSidebar></ProjectTaskSidebar>
            </template>
            <!--  END SIDEBAR  -->

            <!--  BEGIN CONTENT AREA  -->
            <div
                :id="notTasksSideBar ? 'content' : 'task-content'"
                class="main-content"
                :class="{
                    'project-list': isProjectList,
                }"
            >
                <router-view>
                    <!-- BEGIN LOADER -->
                    <div id="load_screen">
                        <div class="loader">
                            <div class="loader-content">
                                <div
                                    class="spinner-grow align-self-center"
                                ></div>
                            </div>
                        </div>
                    </div>
                    <!--  END LOADER -->
                </router-view>
                <!-- BEGIN FOOTER -->
                <div
                    v-show="$store.state.is_loading"
                    class="loader dual-loader mx-auto custom-loading"
                ></div>
                <Footer></Footer>
                <!-- END FOOTER -->
            </div>
            <!--  END CONTENT AREA  -->

            <div
                class="snack-bar d-flex align-items-center"
                :class="$store.state.snackBar.position"
                v-if="$store.state.snackBar.type"
            >
                <b-alert show dismissible :variant="$store.state.snackBar.type">
                    {{ $store.state.snackBar.msg }}
                </b-alert>
            </div>
            <!-- BEGIN APP SETTING LAUNCHER -->
            <!-- <app-settings /> -->
            <!-- END APP SETTING LAUNCHER -->
        </div>
    </div>
</template>
<script>
import Header from "@/components/layout/header.vue";
import Sidebar from "@/components/layout/sidebar.vue";
import ProjectTaskSidebar from "@/components/layout/project-task-sidebar.vue";
import Footer from "@/components/layout/footer.vue";
import appSettings from "@/components/app-settings.vue";
export default {
    components: {
        Header,
        Sidebar,
        ProjectTaskSidebar,
        Footer,
        appSettings,
    },
    computed: {
        notTasksSideBar() {
            if (!this.$route.name?.startsWith("tasks.")) return true;
            return false;
        },
        isProjectList() {
            return (
                this.$route.name?.startsWith("tasks.") &&
                !this.$store.state.ticketSideBar.projectId
            );
        },
    },
    data() {
        return {};
    },
};
</script>
<style>
#task-content {
    position: relative;
    width: 50%;
    flex-grow: 8;
    margin-top: 108px;
    margin-bottom: 0;
    margin-left: 228px;
    transition: 0.6s;
}
.sidebar-closed > #task-content,
#task-content.project-list {
    margin-left: 0;
}
</style>
