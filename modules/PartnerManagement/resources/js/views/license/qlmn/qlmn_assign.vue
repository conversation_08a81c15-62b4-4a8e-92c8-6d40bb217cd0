<template>
    <div class="layout-px-spacing">
        <breadcrumb :lists="breadcrumb_option"></breadcrumb>
        <div class="row layout-top-spacing">
            <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
                <div class="statbox panel box box-shadow">
                    <div class="panel-body">
                        <h6 class="block-title">ĐƠN VỊ HÀNH CHÍNH</h6>
                        <b-form-row>
                            <b-form-group
                                label="Sở giáo dục đào tạo"
                                class="col mr-b-5"
                            >
                                <b-form-select
                                    v-model="account.province_id"
                                    :options="resources.provinces"
                                    value-field="id"
                                    text-field="name"
                                    @input="changeProvince"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.province_id"
                                    >{{ errors.province_id }}</span
                                >
                            </b-form-group>
                            <b-form-group
                                label="Phòng giáo dục đào tạo"
                                class="col mr-b-5"
                            >
                                <b-form-select
                                    v-model="account.district_id"
                                    :options="resources.districts"
                                    value-field="id"
                                    text-field="name"
                                    @input="changeDistrict"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.district_id"
                                    >{{ errors.district_id }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Thuộc Phường/xã" class="col">
                                <b-form-select
                                    v-model="account.ward_id"
                                    :options="resources.wards"
                                    value-field="id"
                                    text-field="name"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.ward_id"
                                    >{{ errors.ward_id }}</span
                                >
                            </b-form-group>
                        </b-form-row>
                        <h6 class="block-title">THÔNG TIN TRƯỜNG HỌC</h6>
                        <b-form-row>
                            <b-form-group label="Tên trường" class="col">
                                <b-input-group>
                                    <b-input
                                        v-model="account.school_name"
                                    ></b-input>
                                </b-input-group>
                                <span
                                    class="text-danger"
                                    v-show="errors.school_name"
                                    >{{ errors.school_name }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Loại hình trường" class="col">
                                <b-form-select
                                    v-model="account.school_level"
                                    :options="resources.school_levels"
                                    value-field="id"
                                    text-field="name"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.school_level"
                                    >{{ errors.school_level }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Số điểm trường" class="col">
                                <b-input
                                    type="text"
                                    v-model="account.school_point"
                                ></b-input>
                                <span
                                    class="text-danger"
                                    v-show="errors.school_point"
                                    >{{ errors.school_point }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Địa chỉ" class="col">
                                <b-input
                                    type="text"
                                    v-model="account.address"
                                ></b-input>
                                <span
                                    class="text-danger"
                                    v-show="errors.address"
                                    >{{ errors.address }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Email" class="col">
                                <b-input-group>
                                    <b-input
                                        type="email"
                                        v-model="account.email"
                                    ></b-input>
                                </b-input-group>
                                <span
                                    class="text-danger"
                                    v-show="errors.email"
                                    >{{ errors.email }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Điện thoại" class="col">
                                <b-input-group>
                                    <b-input
                                        type="tel"
                                        v-model="account.phone"
                                    ></b-input>
                                </b-input-group>
                                <span
                                    class="text-danger"
                                    v-show="errors.phone"
                                    >{{ errors.phone }}</span
                                >
                            </b-form-group>
                        </b-form-row>
                        <h6 class="block-title">SẢN PHẨM</h6>
                        <b-form-row>
                            <b-form-group
                                label="Phân loại sản phẩm"
                                class="col"
                            >
                                <b-form-select
                                    v-model="account.product_id"
                                    :options="resources.products"
                                    value-field="id"
                                    text-field="name"
                                    @input="changeProduct"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.product_id"
                                    >{{ errors.product_id }}</span
                                >
                                <!-- Display pms_project_refs information -->
                                <div
                                    v-if="
                                        pmsProjectRefs && pmsProjectRefs.length
                                    "
                                    class="mt-2 project-refs-container"
                                >
                                    <h6>
                                        <i class="fa-solid fa-list-check"></i>
                                        Gồm các phân hệ:
                                    </h6>
                                    <div class="project-refs-list">
                                        <div
                                            v-for="(
                                                project, index
                                            ) in pmsProjectRefs"
                                            :key="index"
                                            class="project-ref-item"
                                        >
                                            <span class="project-name"
                                                >• {{ project }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </b-form-group>
                            <b-form-group label="Tên tài khoản" class="col">
                                <b-input
                                    type="text"
                                    v-model="account.account"
                                ></b-input>
                                <span
                                    class="text-danger"
                                    v-show="errors.account"
                                    >{{ errors.account }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Thời hạn" class="col">
                                <b-form-select
                                    v-model="account.expired_time"
                                    :options="resources.expired_times"
                                    value-field="id"
                                    text-field="name"
                                    @input="changeExpiredTime"
                                ></b-form-select>
                                <span
                                    class="text-danger"
                                    v-show="errors.expired_time"
                                    >{{ errors.expired_time }}</span
                                >

                                <!-- Display pmsExpiredTime information -->
                                <div
                                    v-if="
                                        pmsExpiredTimeInfo &&
                                        pmsExpiredTimeInfo.months
                                    "
                                    class="mt-2 project-refs-container"
                                >
                                    <h6>
                                        <i class="fa-solid fa-list-check"></i>
                                        Thông tin sử dụng:
                                    </h6>
                                    <div class="project-refs-list">
                                        <div class="project-ref-item">
                                            <span class="project-name"
                                                ><i
                                                    class="fa-regular fa-handshake"
                                                ></i>
                                                Số tháng:
                                                {{ pmsExpiredTimeInfo.months }}
                                                tháng</span
                                            >
                                        </div>

                                        <div class="project-ref-item">
                                            <span class="project-name"
                                                ><i
                                                    class="fa-solid fa-calendar-days"
                                                ></i>
                                                Ngày bắt đầu:
                                                {{
                                                    pmsExpiredTimeInfo.from_date
                                                }}</span
                                            >
                                        </div>
                                        <div class="project-ref-item">
                                            <span class="project-name"
                                                ><i
                                                    class="fa-solid fa-calendar-days"
                                                ></i>
                                                Ngày hết hạn:
                                                {{
                                                    pmsExpiredTimeInfo.to_date
                                                }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </b-form-group>
                            <b-form-group label="Ngày bắt đầu(*)" class="col">
                                <flat-pickr
                                    class="form-control flatpickr active"
                                    v-model="account.from_date"
                                    @input="changeExpiredTime"
                                ></flat-pickr>
                                <span
                                    class="text-danger"
                                    v-show="errors.from_date"
                                    >{{ errors.from_date }}</span
                                >
                            </b-form-group>
                            <b-form-group label="Ngày hết hạn(*)" class="col">
                                <flat-pickr
                                    class="form-control flatpickr active"
                                    v-model="account.to_date"
                                ></flat-pickr>
                                <span
                                    class="text-danger"
                                    v-show="errors.to_date"
                                    >{{ errors.to_date }}</span
                                >
                            </b-form-group>
                        </b-form-row>

                        <b-form-row class="d-flex justify-content-end">
                            <b-button
                                type="button"
                                variant="primary"
                                class="ml-3"
                                @click="submitForm"
                                >Lưu</b-button
                            >
                            <b-button
                                type="button"
                                variant="danger"
                                class="ml-3"
                                to="/contract"
                                >Quay lại</b-button
                            >
                        </b-form-row>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import c from "@/helpers/common";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import "@/assets/sass/forms/custom-flatpickr.css";
import "@/assets/sass/scrollspyNav.scss";
import highlight from "@/components/plugins/highlight.vue";
import "vue-multiselect/dist/vue-multiselect.min.css";
import permission from "@/directive/permission/index.js";
import breadcrumb from "@/views/components/breadcrumb";
import storage from "@/helpers/storage";
import { fetchInfoData } from "@/helpers/partner_auth";

export default {
    directives: { permission },
    components: {
        flatPickr,
        highlight,
        breadcrumb,
    },
    data() {
        return {
            breadcrumb_option: [
                "Cấp tài khoản",
                "Hệ thống quản lý mầm non (PMS+KHGD)",
            ],
            key: "qlmn",
            account: {
                province_id: "",
                district_id: "",
                ward_id: "",
                school_level: "",
                school_name: "",
                school_point: "",
                address: "",
                email: "",
                phone: "",
                account: "",
                expired_time: "",
                product_id: "",
                from_date: new Date().toISOString().slice(0, 10),
                to_date: "",
            },
            errors: {
                province_id: "",
                product_id: "",
                district_id: "",
                ward_id: "",
                school_level: "",
                expired_time: "",
                address: "",
                email: "",
                phone: "",
                from_date: "",
                to_date: "",
            },
            resources: {
                provinces: [{ id: "", name: "- Chọn -" }],
                districts: [{ id: "", name: "- Chọn -" }],
                wards: [{ id: "", name: "- Chọn -" }],
                products: [{ id: "", name: "- Chọn -" }],
                school_levels: [{ id: "", name: "- Chọn -" }],
                expired_times: [{ id: "", name: "- Chọn -" }],
            },
            info: [],
            pmsProjectRefs: [],
            pmsExpiredTimeInfo: {},
        };
    },

    async created() {
        this.$store.commit("toggleLoading", true);

        // Lấy thông tin info từ API lưu vào IndexedDB
        this.info = await fetchInfoData(this.key);

        // Lấy thông tin info từ IndexedDB
        let infoData = await storage.getObject(`info_${this.key}`);

        if (infoData) {
            this.info = infoData;
            // Gán dữ liệu đơn vị
            if (this.info && this.info.data && this.info.data.units) {
                this.resources.provinces = [
                    ...this.resources.provinces,
                    ...this.info.data.units,
                ];
            } else {
                console.log("No units data found in info");
            }

            // Gán dữ liệu sản phẩm
            if (this.info && this.info.data && this.info.data.products) {
                this.resources.products = [
                    ...this.resources.products,
                    ...this.info.data.products,
                ];
            } else {
                console.log("No products data found in info");
            }

            // Gán dữ liệu loại hình trường
            if (this.info && this.info.data && this.info.data.school_levels) {
                this.resources.school_levels = [
                    ...this.resources.school_levels,
                    ...this.info.data.school_levels,
                ];
            } else {
                console.log("No school_levels data found in info");
            }

            // Gán dữ liệu thời gian hết hạn
            if (this.info && this.info.data && this.info.data.expired_times) {
                this.resources.expired_times = [
                    ...this.resources.expired_times,
                    ...this.info.data.expired_times,
                ];
            } else {
                console.log("No expired_times data found in info");
            }
        } else {
            console.log("No data retrieved from IndexedDB");
        }
        this.$store.commit("toggleLoading", false);
    },

    watch: {
        // Watch for changes in the route parameters
        "$route.params.project": {
            immediate: true,
            handler(newProjectId) {
                console.log(
                    `Route parameter 'project' changed to: ${newProjectId}`
                );
                if (newProjectId) {
                    this.loadProjectData(newProjectId);
                }
            },
        },
    },

    methods: {
        // Load data based on project ID
        loadProjectData: async function (projectId) {
            try {
                this.$store.commit("toggleLoading", true);
                console.log(`Loading data for project ID: ${projectId}`);

                // Here you would typically make an API call to get project-specific data
                // For example:
                // const projectData = await c.g(`/api/partners/projects/${projectId}`);

                // Then update your component data with the response
                // For demonstration, we'll just update a property to show it's working
                this.breadcrumb_option = [
                    "Quản lý khách hàng",
                    `Thông tin dự án ${projectId}`,
                ];

                // Reset any previous data if needed
                // this.resetFormData();

                this.$store.commit("toggleLoading", false);
            } catch (error) {
                console.error("Error loading project data:", error);
                this.$store.commit("toggleLoading", false);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi tải dữ liệu dự án!",
                    type: "error",
                });
            }
        },

        async changeProvince() {
            this.$store.commit("toggleLoading", true);

            this.resources.districts = [{ id: "", name: "- Chọn -" }];
            this.resources.wards = [{ id: "", name: "- Chọn -" }];
            this.account.district_id = "";
            this.account.ward_id = "";
            // // Add a 10 second delay before loading districts
            // await new Promise(resolve => setTimeout(resolve, 3000));

            try {
                // Get the selected province ID
                const provinceId = this.account.province_id;
                console.log("Selected province ID:", provinceId);

                if (provinceId) {
                    const selectedProvince = this.resources.provinces.find(
                        (province) => province.id === provinceId
                    );

                    if (selectedProvince) {
                        if (
                            selectedProvince.districts &&
                            Array.isArray(selectedProvince.districts)
                        ) {
                            this.resources.districts = [
                                { id: "", name: "- Chọn -" },
                                ...selectedProvince.districts,
                            ];
                        }
                    }
                }
            } catch (error) {
                console.error("Error setting districts:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi tải dữ liệu quận/huyện!",
                    type: "error",
                });
            } finally {
                this.$store.commit("toggleLoading", false);
            }
        },

        async changeDistrict() {
            this.$store.commit("toggleLoading", true);

            this.resources.wards = [{ id: "", name: "- Chọn -" }];
            this.account.ward_id = "";

            try {
                // Get the selected district ID
                const districtId = this.account.district_id;
                console.log("Selected district ID:", districtId);

                if (districtId) {
                    const selectedDistrict = this.resources.districts.find(
                        (district) => district.id === districtId
                    );

                    if (selectedDistrict) {
                        if (
                            selectedDistrict.wards &&
                            Array.isArray(selectedDistrict.wards)
                        ) {
                            this.resources.wards = [
                                { id: "", name: "- Chọn -" },
                                ...selectedDistrict.wards,
                            ];
                        }
                    }
                }
            } catch (error) {
                console.error("Error setting wards:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi tải dữ liệu phường/xã!",
                    type: "error",
                });
            } finally {
                this.$store.commit("toggleLoading", false);
            }
        },

        async changeProduct() {
            this.$store.commit("toggleLoading", true);
            this.pmsProjectRefs = [];

            try {
                // Get the selected product ID
                const productId = this.account.product_id;
                console.log("Selected product ID:", productId);

                if (
                    productId &&
                    this.info &&
                    this.info.data &&
                    this.info.data.products
                ) {
                    // Find the selected product from the info data
                    const selectedProduct = this.info.data.products.find(
                        (product) => product.id === productId
                    );

                    if (selectedProduct) {
                        console.log("Selected product:", selectedProduct);
                        console.log(
                            "Selected product:",
                            selectedProduct.pms_project_refs
                        );
                        // Check if pms_project_refs exists and is not empty
                        if (selectedProduct.pms_project_refs) {
                            console.log(
                                "Found pms_project_refs:",
                                selectedProduct.pms_project_refs
                            );
                            if (
                                Array.isArray(selectedProduct.pms_project_refs)
                            ) {
                                this.pmsProjectRefs =
                                    selectedProduct.pms_project_refs;
                            } else if (
                                selectedProduct.pms_project_refs &&
                                typeof selectedProduct.pms_project_refs ===
                                    "object"
                            ) {
                                // If it's an object, get its values as an array
                                this.pmsProjectRefs = Object.values(
                                    selectedProduct.pms_project_refs
                                );
                            } else {
                                this.pmsProjectRefs = [];
                            }
                        }
                    } else {
                        console.log("Selected product not found in data");
                    }
                } else {
                    console.log(
                        "No valid product ID or product data available"
                    );
                }
            } catch (error) {
                console.error("Error handling product selection:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi xử lý thông tin sản phẩm!",
                    type: "error",
                });
            } finally {
                this.$store.commit("toggleLoading", false);
            }
        },

        async changeExpiredTime() {
            this.$store.commit("toggleLoading", true);
            this.pmsExpiredTimeInfo = {};

            try {
                // --- Add logic to set to_date = from_date + số tháng ---
                const expiredTimeId = this.account.expired_time;
                const fromDate = this.account.from_date;
                console.log(expiredTimeId + " " + fromDate);
                if (expiredTimeId && fromDate) {
                    // Find the selected expired_time object
                    const expiredTimeObj = this.resources.expired_times.find(
                        (item) => item.id === expiredTimeId
                    );
                    if (expiredTimeObj && expiredTimeObj.id) {
                        // Parse from_date (YYYY-MM-DD)
                        const from = new Date(fromDate);
                        // Add months
                        const months = parseInt(expiredTimeObj.id);
                        if (!isNaN(months)) {
                            const to = new Date(from);
                            to.setMonth(to.getMonth() + months);
                            // Format to YYYY-MM-DD
                            const yyyy = to.getFullYear();
                            const mm = String(to.getMonth() + 1).padStart(
                                2,
                                "0"
                            );
                            const dd = String(to.getDate()).padStart(2, "0");
                            this.account.to_date = `${yyyy}-${mm}-${dd}`;
                            this.pmsExpiredTimeInfo.months = months;
                            this.pmsExpiredTimeInfo.from_date =
                                c.formatDateToDDMMYYYY(fromDate);
                            this.pmsExpiredTimeInfo.to_date =
                                c.formatDateToDDMMYYYY(this.account.to_date);
                        }
                    }
                }
                // --- End logic ---
            } catch (error) {
                console.error("Error handling product selection:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi xử lý thông tin sản phẩm! " + error,
                    type: "error",
                });
            } finally {
                this.$store.commit("toggleLoading", false);
            }
        },

        async submitForm() {
            this.$store.commit("toggleLoading", true);
            const resp = await c.p(`/api/partners/account/qlmn/add`, this.account);
            this.$store.commit("toggleLoading", false);

            console.log(resp);
            if (resp.license) {
                this.$swal({
                    icon: "success",
                    html: `
                        <div class="text-left">
                            <b><i class="fa-solid fa-building"></i> Thông tin đơn vị:</b><br>
                            Tên: <b>${resp.unit?.name || ''}</b><br>
                            Email: <b>${resp.unit?.email || ''}</b><br><br>
                            <b><i class="fa-solid fa-user-tie"></i> Thông tin tài khoản:</b><br>
                            Tài khoản: <b>${resp.user?.username || ''}</b><br>
                            Mật khẩu: <b>qlmn@123456</b> (Nhớ đổi sau khi đăng nhập lần đầu)<br><br>
                            <b><i class="fa-solid fa-barcode"></i> Thông tin giấy phép:</b><br>
                            Mã: <b>${resp.license?.code || ''}</b><br>
                            Ngày kích hoạt: <b>${resp.license?.activated_at_formatted || ''}</b><br>
                            Ngày hết hạn: <b>${resp.license?.expired_at_formatted || ''}</b>
                        </div>
                    `,
                    confirmButtonText: "Chấp nhận",
                    showLoaderOnConfirm: false,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    preConfirm: () => {
                        window.location.href = "/partners/license/list/qlmn";
                    },
                });
            }
        },
    },
};
</script>
<style>
.custom-table tr {
    cursor: pointer;
}

.project-refs-container {
    background-color: #fcfaeb;
    border-radius: 4px;
    padding: 8px 10px;
    margin-top: 8px;
    border: 1px solid #a58629;
    box-shadow: 0 0 6px #ffc10755;
}

.project-refs-container h6 {
    font-size: 12px;
    font-weight: 600;
    color: #3b3f5c;
    margin-bottom: 6px;
    border-bottom: 1px solid #e0e6ed;
    padding-bottom: 4px;
}

.project-ref-item {
    padding: 4px 0;
    margin-bottom: 2px;
    border-bottom: 1px dotted #ebedf2;
}

.project-ref-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.project-name {
    font-size: 12px;
    color: #3b3f5c;
}
</style>
