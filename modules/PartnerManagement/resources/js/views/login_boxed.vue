<template>
    <div class="form auth-boxed">
        <div class="form-container outer">
            <div class="form-form">
                <div class="form-form-wrap">
                    <div class="form-container">
                        <div class="form-content">
                            <h1 class="">
                                <img src="/images/partner.svg" width="48px" height="48px"> ĐỐI TÁC
                                VIETEC
                            </h1>
                            <p class="">Đ<PERSON>ng nhập tài khoản để tiếp tục.</p>

                            <b-form
                                class="text-left"
                                @submit.prevent="loginSubmit"
                            >
                                <div class="form">
                                    <div
                                        id="username-field"
                                        class="field-wrapper input"
                                    >
                                        <label for="username"
                                            >Tên đăng nhập</label
                                        >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="feather feather-user"
                                        >
                                            <path
                                                d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
                                            ></path>
                                            <circle
                                                cx="12"
                                                cy="7"
                                                r="4"
                                            ></circle>
                                        </svg>
                                        <b-input
                                            type="email"
                                            required
                                            placeholder="Tên đăng nhập"
                                            v-model="form.email"
                                        ></b-input>
                                    </div>

                                    <div
                                        id="password-field"
                                        class="field-wrapper input mb-2"
                                    >
                                        <div
                                            class="d-flex justify-content-between"
                                        >
                                            <label for="password"
                                                >Mật khẩu</label
                                            >
                                        </div>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="feather feather-lock"
                                        >
                                            <rect
                                                x="3"
                                                y="11"
                                                width="18"
                                                height="11"
                                                rx="2"
                                                ry="2"
                                            ></rect>
                                            <path
                                                d="M7 11V7a5 5 0 0 1 10 0v4"
                                            ></path>
                                        </svg>
                                        <b-input
                                            :type="pwd_type"
                                            required
                                            placeholder=""
                                            v-model="form.password"
                                            minlength="1"
                                        ></b-input>
                                        <svg
                                            @click="set_pwd_type"
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            id="toggle-password"
                                            class="feather feather-eye"
                                        >
                                            <path
                                                d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                                            ></path>
                                            <circle
                                                cx="12"
                                                cy="12"
                                                r="3"
                                            ></circle>
                                        </svg>
                                    </div>
                                    <div
                                        class="d-sm-flex justify-content-between"
                                    >
                                        <div class="field-wrapper">
                                            <b-button
                                                type="submit"
                                                variant="success"
                                                >Đăng nhập</b-button
                                            >
                                        </div>
                                    </div>
                                    <!-- <p class="signup-link">Not registered ? <router-link to="/auth/register-boxed">Create an account</router-link></p> -->
                                </div>
                            </b-form>
                            <br />
                            <b-alert
                                variant="light-danger"
                                class="border-0 mb-4"
                            >
                                <strong>Lỗi!</strong> {{ text_error }}
                            </b-alert>
                            <b-alert
                                v-model="showDismissibleAlert"
                                variant="light-danger"
                                dismissible
                            >
                                {{ text_error }}
                            </b-alert>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import "@/assets/sass/authentication/auth-boxed.scss";
import { partner_login } from "@/helpers/partner_auth";
import moment from "moment";

export default {
    metaInfo: { title: "Đối tác - Đăng nhập" },
    data() {
        return {
            pwd_type: "password",
            form: {
                email: "",
                password: "",
            },
            type: "login",
            error: null,
            showDismissibleAlert: false,
            text_error: "",
        };
    },
    mounted() {},
    methods: {
        set_pwd_type() {
            if (this.pwd_type == "password") {
                this.pwd_type = "text";
            } else {
                this.pwd_type = "password";
            }
        },
        loginSubmit() {
            this.$store.dispatch("PARTNER_LOGIN");

            partner_login(this.$data.form).then((res) => {
                console.log(res);
                if (res.success) {
                    this.$store.commit("PARTNER_LOGIN_SUCCESS", res);

                    this.text_error= "Đăng nhập thành công";
                    this.showAlert();
                    this.$router.push({ path: "/partners/dashboard" });
                } else {
                    this.$store.commit("PARTNER_LOGIN_FAILED", { res });
                    this.text_error = res.message;
                    this.showAlert();
                }
            });
        },
        showAlert() {
            this.showDismissibleAlert = true;
        },
    },
    computed: {},
};
</script>
