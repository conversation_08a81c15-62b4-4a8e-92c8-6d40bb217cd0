<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePmsProjectsReferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pms_projects_references', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pms_project_id')->comment('Foreign key to PMS projects table');
            $table->string('pms_project_name')->comment('Name of the PMS project');
            $table->integer('status')->default(1)->comment('Status of the reference');
            $table->unsignedBigInteger('product_id')->comment('Foreign key to m_products table');
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index('pms_project_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pms_projects_references');
    }
}
