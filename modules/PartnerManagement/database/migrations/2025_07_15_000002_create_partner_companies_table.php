<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartnerCompaniesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('partner_companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('short_name')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamps();
        });

        // Add company_id to partners table
        Schema::table('partners', function (Blueprint $table) {
            $table->unsignedBigInteger('company_id')->nullable()->after('status');
            $table->foreign('company_id')->references('id')->on('partner_companies')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove foreign key and company_id from partners table
        Schema::table('partners', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropColumn('company_id');
        });

        Schema::dropIfExists('partner_companies');
    }
}
