<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePmsProjectProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pms_project_product', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pms_project_id')->comment('Foreign key to PMS projects table');
            $table->unsignedBigInteger('product_id')->comment('Foreign key to m_products table');
            $table->timestamps();
            
            // Add unique constraint for the combination of pms_project_id and product_id
            $table->unique(['pms_project_id', 'product_id'], 'pms_project_product_pms_project_id_product_id_unique');
            
            // Add indexes for better performance
            $table->index('pms_project_id', 'pms_project_product_pms_project_id_index');
            $table->index('product_id', 'pms_project_product_product_id_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pms_project_product');
    }
}
