<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePartnerLicensesAddMonthTypeProjectUnitRef extends Migration
{
    public function up()
    {
        Schema::table('partner_licenses', function (Blueprint $table) {
            $table->integer('month', false, true)->length(3)->nullable()->default(1)->after('assigned_at');
            $table->tinyInteger('type')->nullable()->default(1)->comment('1 - new, 2 - renew, 3 - upgrade')->after('month');
            $table->string('project_unit_ref', 200)->nullable()->after('type');
        });
    }

    public function down()
    {
        Schema::table('partner_licenses', function (Blueprint $table) {
            $table->dropColumn(['month', 'type', 'project_unit_ref']);
        });
    }
}
