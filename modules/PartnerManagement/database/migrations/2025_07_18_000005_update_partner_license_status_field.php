<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdatePartnerLicenseStatusField extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('partner_licenses', function (Blueprint $table) {
            // First drop the default constraint
            $table->boolean('status')->default(null)->change();
            
            // Then change the column type to integer
            $table->integer('status')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('partner_licenses', function (Blueprint $table) {
            // First drop the default constraint
            $table->integer('status')->default(null)->change();
            
            // Then change the column type back to boolean
            $table->boolean('status')->default(false)->change();
        });
    }
}
