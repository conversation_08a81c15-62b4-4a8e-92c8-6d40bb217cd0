<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartnerRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create roles for the partner-api guard
        $tableNames = config('permission.table_names');

        // Create roles for partner-api guard if they don't exist
        Schema::table($tableNames['roles'], function (Blueprint $table) {
            // Check if the guard_name index exists
            $sm = Schema::getConnection()->getDoctrineSchemaManager();
            $indexesFound = $sm->listTableIndexes($table->getTable());

            if (isset($indexesFound['roles_name_guard_name_unique'])) {
                // Drop the existing unique index
                $table->dropUnique('roles_name_guard_name_unique');

                // Recreate it to ensure it includes our new guard
                $table->unique(['name', 'guard_name']);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('partner_roles');
    }
}
