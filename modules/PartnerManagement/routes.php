<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\PartnerManagement\Http\Controllers\PartnerController;
use Mo<PERSON>les\PartnerManagement\Http\Controllers\AuthController;
use Modules\PartnerManagement\Http\Controllers\PartnerLicenseController;
use Mo<PERSON>les\PartnerManagement\Http\Controllers\PartnerNotificationController;
use Modules\PartnerManagement\Http\Controllers\AccountController;

// Partner Authentication Routes
Route::group([
    'prefix' => 'api/partners/auth',
], function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);

    Route::group(['middleware' => 'auth:partner-api'], function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
        Route::get('/profile', [AuthController::class, 'profile']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
    });
});


// Partner API Routes (for partners to access)
Route::group([
    'middleware' => 'auth:partner-api',
    'prefix' => 'api/partners',
], function () {
    // Routes that require partner-admin role
    Route::group(['middleware' => 'partner.role:partner-user'], function () {
        Route::get('/license/list', [PartnerLicenseController::class, 'index'])->name('partner.license.list');
        Route::post('/license/update/{id}', [PartnerLicenseController::class, 'update'])->name('partner.license.update');
        Route::post('/license/lock/{id}', [PartnerLicenseController::class, 'lock'])->name('partner.license.lock');
        Route::post('/license/unlock/{id}', [PartnerLicenseController::class, 'unlock'])->name('partner.license.unlock');
        Route::post('/license/renew/{id}', [PartnerLicenseController::class, 'renew'])->name('partner.license.renew');
    });

    // Routes that require specific permissions
    Route::get('/profile', function () {
        return response()->json(['partner' => auth('partner-api')->user()]);
    })->middleware('partner.permission:partner_view');

    Route::post('/pusher/check-auth', [AuthController::class, 'pusherCheckAuth'])->name('partner.pusher.check-auth');
    Route::get('/partner/notifications', [PartnerController::class, 'list_notifications'])->name('partner.notifications');
    Route::post('/partner/notification/mark_as_read/{id}', [PartnerController::class, 'mark_as_read'])->name('partner.notification.mark_as_read');
    Route::post('/partner/notification/mark_as_read_all', [PartnerController::class, 'mark_as_read_all'])->name('partner.notification.mark_as_read_all');
    Route::post('/account/get_project_info/{code}', [AccountController::class, 'project_info'])->name('partner.account.get_project_info');
    Route::post('/account/qlmn/add', [AccountController::class, 'qlmn_add'])->name('partner.account.qlmn_add');

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [PartnerNotificationController::class, 'index'])->name('partner.notifications.index');
        Route::get('/unread', [PartnerNotificationController::class, 'unread'])->name('partner.notifications.unread');
        Route::post('/{id}/read', [PartnerNotificationController::class, 'markAsRead'])->name('partner.notifications.mark-as-read');
        Route::post('/read-all', [PartnerNotificationController::class, 'markAllAsRead'])->name('partner.notifications.mark-all-as-read');
    });
});

// Admin API Routes (for admin to manage partners)
Route::group([
    'middleware' => ['auth:api', 'permission:partner_management'],
    'prefix' => 'api/admin/partners',
], function () {
    // Send notification to partner
    Route::post('/notifications/send', [PartnerNotificationController::class, 'send'])->name('admin.partner.notifications.send');
});