<?php

namespace Modules\PartnerManagement\Policies;

use Modules\PartnerManagement\Models\Partner;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PartnerPolicy
{
    use HandlesAuthorization;

    public function __construct()
    {
        //
    }

    public function viewAny(User $user)
    {
        return $user->hasPermissionTo('partner_list');
    }

    public function view(User $user, Partner $partner)
    {
        return $user->hasPermissionTo('partner_view');
    }

    public function create(User $user)
    {
        return $user->hasPermissionTo('partner_add');
    }

    public function update(User $user, Partner $partner)
    {
        return $user->hasPermissionTo('partner_edit');
    }

    public function delete(User $user, Partner $partner)
    {
        return $user->hasPermissionTo('partner_delete');
    }
}
