<?php

namespace Modules\PartnerManagement\Tests;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mo<PERSON>les\PartnerManagement\Jobs\ToggleLicenseStatusJob;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Models\PMS\PMSUnit;

class ToggleLicenseStatusJobTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_lock_license_and_deactivate_pms_unit()
    {
        // Tạo test data
        $license = PartnerLicense::factory()->create([
            'status' => PartnerLicense::STATUS_ACTIVATED,
            'company_id' => 1,
            'project_unit_ref' => 123,
            'expired_at' => now()->addDays(30)
        ]);

        $pmsUnit = PMSUnit::factory()->create([
            'id' => 123,
            'status' => 1
        ]);

        // Thực hiện lock
        $job = new ToggleLicenseStatusJob($license->id, 'lock', 1);
        $result = $job->handle();

        // Kiểm tra kết quả
        $this->assertTrue($result['success']);
        $this->assertEquals('Khóa giấy phép thành công!', $result['message']);

        // Kiểm tra license đã được cập nhật
        $license->refresh();
        $this->assertEquals(PartnerLicense::STATUS_PENDING, $license->status);

        // Kiểm tra PMSUnit đã được deactivate
        $pmsUnit->refresh();
        $this->assertEquals(0, $pmsUnit->status);
    }

    /** @test */
    public function it_can_unlock_license_and_activate_pms_unit()
    {
        // Tạo test data
        $license = PartnerLicense::factory()->create([
            'status' => PartnerLicense::STATUS_PENDING,
            'company_id' => 1,
            'project_unit_ref' => 123,
            'expired_at' => now()->addDays(30)
        ]);

        $pmsUnit = PMSUnit::factory()->create([
            'id' => 123,
            'status' => 0
        ]);

        // Thực hiện unlock
        $job = new ToggleLicenseStatusJob($license->id, 'unlock', 1);
        $result = $job->handle();

        // Kiểm tra kết quả
        $this->assertTrue($result['success']);
        $this->assertEquals('Mở khóa giấy phép thành công!', $result['message']);

        // Kiểm tra license đã được cập nhật
        $license->refresh();
        $this->assertEquals(PartnerLicense::STATUS_ACTIVATED, $license->status);

        // Kiểm tra PMSUnit đã được activate
        $pmsUnit->refresh();
        $this->assertEquals(1, $pmsUnit->status);
    }

    /** @test */
    public function it_cannot_lock_expired_license()
    {
        // Tạo license đã hết hạn
        $license = PartnerLicense::factory()->create([
            'status' => PartnerLicense::STATUS_ACTIVATED,
            'company_id' => 1,
            'project_unit_ref' => 123,
            'expired_at' => now()->subDays(1) // đã hết hạn
        ]);

        // Thực hiện lock
        $job = new ToggleLicenseStatusJob($license->id, 'lock', 1);
        $result = $job->handle();

        // Kiểm tra kết quả
        $this->assertFalse($result['success']);
        $this->assertEquals('Không thể khóa giấy phép đã hết hạn!', $result['message']);

        // Kiểm tra license không thay đổi
        $license->refresh();
        $this->assertEquals(PartnerLicense::STATUS_ACTIVATED, $license->status);
    }
}
