<?php

namespace Modules\PartnerManagement\Transformers;

use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;
use League\Fractal;
use Modules\PartnerManagement\Transformers\PMSUnitTransformer;
use Cache;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Transformers\PartnerActivityLogTransformer;

class PartnerLicenseTransformer extends TransformerAbstract
{
    protected $cacheTime;

    public function __construct()
    {
        $this->cacheTime = env('CACHE_EXPIRED_TIME', 3600);
    }

    public function transform($item)
    {
        // Nếu là paginator thì dùng transformPaginate
        if ($item instanceof \Illuminate\Pagination\LengthAwarePaginator) {
            return $this->transformPaginate($item);
        }

        $fieldLabels = [
            'activated_at' => 'Thời gian kích hoạt',
            'expired_at' => 'Thời gian hết hạn',
            'project_account' => 'Tài khoản',
            'status' => 'Trạng thái',
            'assigned_at' => 'Ngày cấp tài khoản cho khách hàng',
            'project_code' => 'Dự án',
            'project_unit_ref' => 'Trường',
            'company_id' => 'Đối tác'
        ];

        $fractal = new \League\Fractal\Manager();
        
        // Cache activities cho mỗi license
        $activities = Cache::remember('partner_license_activities_' . $item->id, $this->cacheTime, function() use ($item, $fractal, $fieldLabels) {
            return $fractal->createData(new Fractal\Resource\Collection($item->getActivityLogs(25), new PartnerActivityLogTransformer($fieldLabels)))->toArray()['data'];
        });

        // Cache thông tin đơn vị
        $unitInfo = Cache::remember('partner_license_unit_' . $item->project_unit_ref, $this->cacheTime, function() use ($item) {
            if (!$item->pms_unit) {
                return [
                    'unit_name' => null,
                    'province_name' => null,
                    'district_name' => null,
                    'ward_name' => null
                ];
            }
            
            return [
                'unit_name' => $item->pms_unit->name,
                'province_name' => $item->pms_unit->parentDepartment->parent->name,
                'district_name' => $item->pms_unit->parentDepartment->name,
                'ward_name' => $item->pms_unit->a_ward->name
            ];
        });

        return [
            'id' => $item->id,
            'code' => $item->code,
            'month' => $item->month,
            'type' => $item->type,
            'project_code' => $item->project_code,
            'project_account' => $item->project_account,
            'activated_at' => $item->activated_at_en,
            'expired_at' => $item->expired_at_en,
            'activated_at_vn' => $item->activated_at_formatted,
            'expired_at_vn' => $item->expired_at_formatted,
            'assigned_at' => $item->assigned_at_formatted,
            'status' => $item->status,
            'status_label' => $item->status_label,
            'project_unit_ref' => $item->project_unit_ref,
            'project_unit_name' => $unitInfo['unit_name'],
            'province_name' => $unitInfo['province_name'],
            'district_name' => $unitInfo['district_name'],
            'ward_name' => $unitInfo['ward_name'],
            'activities' => $activities,
        ];
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }

    /**
     * Transform a paginated collection.
     *
     * @param \Illuminate\Pagination\LengthAwarePaginator $paginator
     * @return array
     */
    public function transformPaginate($paginator)
    {
        return [
            'data' => $this->transformCollection(collect($paginator->items())),
            'current_page' => $paginator->currentPage(),
            'pagination' => [
                'total' => $paginator->total(),
                'per_page' => $paginator->perPage(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
        ];
    }
}
