<?php

namespace Modules\PartnerManagement\Transformers;

use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSWard;

class PMSUnitTransformer extends TransformerAbstract
{
    public function transform(PMSUnit $item)
    {
        $row = [
            'id' => $item->id,
            'name' => $item->name,
            'level' => $item->level
        ];

        if ($item->level == 2 && isset($item->districts) && is_iterable($item->districts)) {
            $row['districts'] = $this->transformCollection($item->districts);
        }
        // if ($item->level == 3 && isset($item->schools) && is_iterable($item->schools)) {
        //     $row['schools'] = $this->transformCollection($item->schools);
        //     unset($row['districts']);
        // }

        if ($item->level == 4) {
            unset($row['districts']);
            unset($row['schools']);
        }

        if ($item->level == 3) {
            unset($row['districts']);
            $row['wards'] = PMSWard::where('district', $item->district)
                ->get()
                ->map(function ($ward) {
                    return [
                        'id' => $ward->id,
                        'name' => $ward->name,
                    ];
                })
                ->toArray();
        }



        return $row;
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }
}
