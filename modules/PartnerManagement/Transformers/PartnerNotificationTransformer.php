<?php

namespace Modules\PartnerManagement\Transformers;

use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;

class PartnerNotificationTransformer extends TransformerAbstract
{
    public function transform(DatabaseNotification $item)
    {

        return [
            'id' => $item->id,
            'title' => $item->data['title'] ?? null,
            'content' => $item->data['body'] ?? null,
            'additional' => $item->data['data'] ?? null,
            'read_at' => $item->read_at,
            'created_at' => $item->created_at,
            'type' => $item->data['type'] ?? null
        ];
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }

}
