<?php

namespace Modules\PartnerManagement\Transformers;

use App\Models\Product;
use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSWard;

class ProductTransformer extends TransformerAbstract
{
    public function transform(Product $item)
    {
        $row = [
            'id' => $item->id,
            'name' => $item->name,
            'code' => $item->code,
            'short_name' => $item->short_name
        ];

        if (isset($item->pmsProjectRefs) && is_iterable($item->pmsProjectRefs)) {
            $row['pms_project_refs'] = $item->pmsProjectRefs()->pluck('pms_project_name', 'pms_project_id');
        }

        return $row;
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }
}
