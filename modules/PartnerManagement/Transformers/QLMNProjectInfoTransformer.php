<?php

namespace Modules\PartnerManagement\Transformers;

use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;
use League\Fractal;
use Modules\PartnerManagement\Transformers\PMSUnitTransformer;
use Cache;

class QLMNProjectInfoTransformer extends TransformerAbstract
{
    public function transform($item)
    {
        

        return Cache::remember('project_info_qlmn', env('CACHE_EXPIRED_TIME'), function () use($item) {
            $fractal = new \League\Fractal\Manager();
            return [
                'products' => $fractal->createData(new Fractal\Resource\Collection($item['products'], new ProductTransformer()))->toArray()['data'],
                'units' => $fractal->createData(new Fractal\Resource\Collection($item['units'], new PMSUnitTransformer()))->toArray()['data'],
                'school_levels' => $item['school_levels'],
                'expired_times' => [
                    ['id' => 1, 'name' => '1 tháng (demo)'],
                    ['id' => 3, 'name' => '3 tháng'],
                    ['id' => 6, 'name' => '6 tháng'],
                    ['id' => 12, 'name' => '12 tháng'],
                    ['id' => 24, 'name' => '24 tháng'],
                    ['id' => 36, 'name' => '36 tháng'],
                ]
            ];
        });
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }
}
