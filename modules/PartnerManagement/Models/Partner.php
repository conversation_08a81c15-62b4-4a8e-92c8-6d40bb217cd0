<?php

namespace Modules\PartnerManagement\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use <PERSON><PERSON>\Permission\Traits\HasRoles;
use Mo<PERSON>les\PartnerManagement\Events\PartnerNotificationEvent;
use Modules\PartnerManagement\Notifications\PartnerNotification;

class Partner extends Authenticatable implements JWTSubject
{
    use Notifiable;
    use HasRoles;

    protected $fillable = [
        'name',
        'code',
        'email',
        'password',
        'phone',
        'address',
        'description',
        'status',
        'company_id'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'status' => 'boolean',
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Get the company that the partner belongs to.
     */
    public function company()
    {
        return $this->belongsTo(PartnerCompany::class, 'company_id');
    }

    /**
     * Send a notification to the partner via Pusher
     *
     * @param string $title The notification title
     * @param string $body The notification body
     * @param string $type The notification type
     * @param array $data Additional data to include in the notification
     * @param string $option The notification option (add, update, delete)
     * @return void
     */
    public function sendPusherNotification($title, $body, $type, $data = [], $option = 'add')
    {
        // Create the notification message
        $message = [
            'title' => $title,
            'body' => $body,
            'type' => $type,
            'option' => $option,
            'data' => $data,
            'created_at' => now()->toDateTimeString()
        ];

        // Store notification in database
       $this->notify(new PartnerNotification($message));

        // Get the latest notification for this partner to retrieve its ID
        $latestNotification = $this->notifications()->latest()->first();
        $notificationId = $latestNotification ? $latestNotification->id : null;

        $message['id'] = $notificationId;
        // dd($notification);
        // Broadcast the notification via Pusher
        event(new PartnerNotificationEvent($this, $message));
    }
}
