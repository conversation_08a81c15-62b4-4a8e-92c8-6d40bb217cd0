<?php

namespace Modules\PartnerManagement\Models\PMS;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PMSUser extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_pms';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [

    ];

    /**
     * Get the province that owns the school.
     */
    public function a_province()
    {
        return $this->belongsTo(PMSProvince::class, 'province', 'id');
    }

    /**
     * Get the district that owns the school.
     */
    public function a_district()
    {
        return $this->belongsTo(PMSDistrict::class, 'district', 'id');
    }

    /**
     * Get the ward that owns the school.
     */
    public function a_ward()
    {
        return $this->belongsTo(PMSWard::class, 'ward', 'id');
    }

    /**
     * Get the parent unit.
     */
    public function unit()
    {
        return $this->belongsTo(PMSUnit::class, 'unit_id', 'id');
    }
}
