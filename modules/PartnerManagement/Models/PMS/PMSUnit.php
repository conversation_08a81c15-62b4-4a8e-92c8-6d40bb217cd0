<?php

namespace Modules\PartnerManagement\Models\PMS;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PMSUnit extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_pms';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'units';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [

    ];

    /**
     * Get the province that owns the school.
     */
    public function a_province()
    {
        return $this->belongsTo(PMSProvince::class, 'province', 'id');
    }

    /**
     * Get the district that owns the school.
     */
    public function a_district()
    {
        return $this->belongsTo(PMSDistrict::class, 'district', 'id');
    }

    /**
     * Get the ward that owns the school.
     */
    public function a_ward()
    {
        return $this->belongsTo(PMSWard::class, 'ward', 'id');
    }

    /**
     * Get the parent unit.
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * Get the users for the unit.
     */
    public function users()
    {
        return $this->hasMany(PMSUser::class, 'unit_id', 'id');
    }

    /**
     * Get the project permissions for the unit.
     */
    public function project_permissions()
    {
        return $this->hasMany(PMSProjectPermission::class, 'unit_id', 'id');
    }
    
    /**
     * Scope a query to only include Phòng giáo dục.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePhongGiaoDuc($query)
    {
        return $query->where('level', '3');
    }

    /**
     * Scope a query to only include Sở giáo dục.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSoGiaoDuc($query)
    {
        return $query->where('level', '2');
    }

    /**
     * Scope a query to only include Trường học.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTruongHoc($query)
    {
        return $query->where('level', '4');
    }

    public function scopeIsActive($query)
    {
        return $query->where('status', '1');
    }

    public function districts()
    {
        return $this->hasMany(PMSUnit::class, 'parent_id', 'id')->phongGiaoDuc();
    }

    public function schools()
    {
        return $this->hasMany(PMSUnit::class, 'parent_id', 'id')->truongHoc();
    }

    /**
     * Get all schools under this department of education (So GD)
     * This method should be called on a Sở GD unit (level 2)
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllSchools()
    {
        return PMSUnit::query()
            ->truongHoc()
            ->isActive()
            ->whereIn('parent_id', function ($query) {
                $query->select('id')
                    ->from('units')
                    ->where('parent_id', $this->id)
                    ->where('level', '3'); // Phòng giáo dục level
            })
            ->get();
    }

    /**
     * Get all schools under this department (Phong GD)
     * This method should be called on a Phòng GD unit (level 3)
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDepartmentSchools()
    {
        return PMSUnit::query()
            ->truongHoc()
            ->isActive()
            ->where('parent_id', $this->id)
            ->get();
    }

    /**
     * Get parent department (Phòng GD) from a school unit
     * This method should be called on a Trường học unit (level 4)
     *
     * @return PMSUnit|null
     */
    public function getParentDepartment()
    {
        if ($this->level !== '4') {
            return null;
        }

        return PMSUnit::query()
            ->phongGiaoDuc()
            ->isActive()
            ->where('id', $this->parent_id)
            ->first();
    }

    /**
     * Get parent department of education (Sở GD) from a school unit
     * This method should be called on a Trường học unit (level 4)
     *
     * @return PMSUnit|null
     */
    public function getParentDepartmentOfEducation()
    {
        if ($this->level !== '4') {
            return null;
        }

        $phongGD = $this->getParentDepartment();
        if (!$phongGD) {
            return null;
        }

        return PMSUnit::query()
            ->soGiaoDuc()
            ->isActive()
            ->where('id', $phongGD->parent_id)
            ->first();
    }

    /**
     * Get the parent department (Phòng GD).
     * This relationship should be used on a Trường học unit (level 4)
     */
    public function parentDepartment()
    {
        return $this->belongsTo(self::class, 'parent_id')
            ->phongGiaoDuc()
            ->isActive();
    }

    /**
     * Get the parent department of education (Sở GD).
     * This relationship should be used on a Trường học unit (level 4)
     */
    public function parentDepartmentOfEducation()
    {
        return $this->parentDepartment()
            ->with(['parent' => function ($query) {
                $query->soGiaoDuc()->isActive();
            }]);
    }

    /**
     * Get full hierarchy names from school to department of education
     * This method should be called on a Trường học unit (level 4)
     *
     * @return array|null Returns array with keys 'school', 'department', 'department_of_education'
     */
    public function getFullHierarchyNames()
    {
        if ($this->level !== '4') {
            return null;
        }

        // Load relationships
        $this->load(['parentDepartment.parent' => function ($query) {
            $query->soGiaoDuc()->isActive();
        }]);

        $result = [
            'school' => $this->name,
            'department' => null,
            'department_of_education' => null
        ];

        if ($this->parentDepartment) {
            $result['department'] = $this->parentDepartment->name;
            
            if ($this->parentDepartment->parent) {
                $result['department_of_education'] = $this->parentDepartment->parent->name;
            }
        }

        return $result;
    }
}
