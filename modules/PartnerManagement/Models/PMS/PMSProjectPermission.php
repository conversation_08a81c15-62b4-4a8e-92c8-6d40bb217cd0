<?php

namespace Modules\PartnerManagement\Models\PMS;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PMSProjectPermission extends Model
{
    use HasFactory;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_pms';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'project_permissions';
    public $timestamps = true;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = ['unit_id', 'project_id'];
    public $incrementing = false;
    protected $keyType = 'array';


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'actived_at',
        'deactived_at'
    ];

   
    /**
     * Get the parent project.
     */
    public function project()
    {
        return $this->belongsTo(PMSProject::class, 'project_id', 'id');
    }

    /**
     * Get the unit.
     */
    public function unit()
    {
        return $this->belongsTo(PMSUnit::class, 'unit_id', 'id');
    }
}
