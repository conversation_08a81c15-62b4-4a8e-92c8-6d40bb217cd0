<?php

namespace Modules\PartnerManagement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\Uuids;

use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions; // Required for Laravel 8+ <PERSON><PERSON> config
use App\Models\MongoActivity;
use Jen<PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;

class PartnerLicense extends Model
{
    use HasFactory, Uuids, LogsActivity, HybridRelations;

    /**
     * License status constants
     */
    const STATUS_CREATED = 0;
    const STATUS_ASSIGNED = 1;
    const STATUS_ACTIVATED = 2;
    const STATUS_EXPIRED = 3;
    const STATUS_PENDING = 4;

    /**
     * License status labels
     */
    const STATUS_LABELS = [
        self::STATUS_CREATED => 'Mới',
        self::STATUS_ASSIGNED => 'Cấp cho đối tác',
        self::STATUS_ACTIVATED => 'Đ<PERSON> kích hoạt',
        self::STATUS_EXPIRED => 'Hết hạn',
        self::STATUS_PENDING => 'Tạm khóa',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'partner_licenses';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'status',
        'company_id',
        'project_code',
        'project_account',
        'activated_at',
        'expired_at',
        'assigned_at',
        'project_unit_ref',
        'month',
        'type',
    ];

    protected $appends = [
        'activated_at_formatted',
        'expired_at_formatted',
        'assigned_at_formatted',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'integer',
        'activated_at' => 'datetime',
        'expired_at' => 'datetime',
        'assigned_at' => 'datetime',
    ];

    /**
     * Get the company that the license belongs to.
     */
    public function company()
    {
        return $this->belongsTo(PartnerCompany::class, 'company_id');
    }

    /**
     * Get the status label.
     *
     * @return string
     */
    public function getStatusLabelAttribute()
    {
        return self::STATUS_LABELS[$this->status] ?? 'Unknown';
    }

    public function getExpiredAtEnAttribute()
    {
        return $this->expired_at
            ? $this->expired_at->format('Y-m-d H:i:s')
            : null;
    }

    public function getActivatedAtEnAttribute()
    {
        return $this->activated_at
            ? $this->activated_at->format('Y-m-d H:i:s')
            : null;
    }

    /**
     * Get formatted activated_at.
     */
    public function getActivatedAtFormattedAttribute()
    {
        return $this->activated_at
            ? $this->activated_at->format('d/m/Y H:i:s')
            : null;
    }

    /**
     * Get formatted expired_at.
     */
    public function getExpiredAtFormattedAttribute()
    {
        return $this->expired_at
            ? $this->expired_at->format('d/m/Y H:i:s')
            : null;
    }

    /**
     * Get formatted assigned_at.
     */
    public function getAssignedAtFormattedAttribute()
    {
        return $this->assigned_at
            ? $this->assigned_at->format('d/m/Y H:i:s')
            : null;
    }

    /**
     * Get all activity logs for this license (Spatie Activitylog)
     */
    public function activities()
    {
        return $this->morphMany(MongoActivity::class, 'subject');
    }

    /**
     * Helper to get activity logs for this instance
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActivityLogs($limit = 50)
    {
        return $this->activities()->latest()->limit($limit)->get();
    }

    /**
     * Scope a query to filter by company_id.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForAuthCompany($query)
    {
        $companyId = auth('partner-api')->user()->company_id;
        return $query->where('company_id', $companyId);
    }

    /**
     * Get the PMSUnit associated with the license.
     */
    public function pms_unit()
    {
        return $this->belongsTo(\Modules\PartnerManagement\Models\PMS\PMSUnit::class, 'project_unit_ref', 'id');
    }

    public function district()
    {
        return $this->belongsTo(\Modules\PartnerManagement\Models\PMS\PMSUnit::class, 'project_unit_ref', 'id')->phongGiaoDuc();
    }

    /**
     * Check if a project_unit_ref has an active license (status = 2)
     *
     * @param string $projectUnitRef
     * @return bool
     */
    public static function hasActiveLicense($projectUnitRef)
    {
        return self::where('project_unit_ref', $projectUnitRef)
            ->where('status', self::STATUS_ACTIVATED)
            ->exists();
    }

    /**
     * Get active license for a project_unit_ref
     *
     * @param string $projectUnitRef
     * @return PartnerLicense|null
     */
    public static function getActiveLicense($projectUnitRef)
    {
        return self::where('project_unit_ref', $projectUnitRef)
            ->where('status', self::STATUS_ACTIVATED)
            ->first();
    }

    /**
     * Define what to log for Spatie Activitylog (Laravel 8+ syntax)
     *
     * @return \Spatie\Activitylog\LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()        // Log all fillable attributes
            ->logOnlyDirty()       // Only log changed attributes
            ->dontSubmitEmptyLogs() // Don't log if nothing changed
            ->useLogName('partner-license'); // Custom log name for filtering
    }
}
