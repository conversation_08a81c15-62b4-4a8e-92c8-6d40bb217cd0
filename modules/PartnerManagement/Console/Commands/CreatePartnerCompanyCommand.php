<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Modules\PartnerManagement\Models\PartnerCompany;
use Illuminate\Support\Facades\Validator;

class CreatePartnerCompanyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'partner:create-company {--name= : The name of the company}
                                                  {--short_name= : The short name of the company}
                                                  {--prefix_code= : The prefix code for the company (max 100 chars)}
                                                  {--status=1 : The status of the company (1: active, 0: inactive)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new partner company';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get options or prompt for input
        $name = $this->option('name');
        if (empty($name)) {
            $name = $this->ask('Enter company name');
        }

        $shortName = $this->option('short_name');
        if (empty($shortName)) {
            $shortName = $this->ask('Enter company short name (optional)');
        }

        $prefixCode = $this->option('prefix_code');
        if (empty($prefixCode)) {
            $prefixCode = $this->ask('Enter company prefix code (optional, max 100 chars)');
        }

        $status = $this->option('status');
        if (!in_array($status, ['0', '1'])) {
            $status = $this->choice('Select company status', ['Inactive', 'Active'], 1);
            $status = $status === 'Active' ? 1 : 0;
        }

        // Validate input
        $validator = Validator::make([
            'name' => $name,
            'short_name' => $shortName,
            'prefix_code' => $prefixCode,
            'status' => $status,
        ], [
            'name' => 'required|string|max:255',
            'short_name' => 'nullable|string|max:50',
            'prefix_code' => 'nullable|string|max:100',
            'status' => 'boolean',
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        // Create the company
        try {
            $company = PartnerCompany::create([
                'name' => $name,
                'short_name' => $shortName,
                'prefix_code' => $prefixCode,
                'status' => (bool) $status,
            ]);

            $this->info("Partner company created successfully!");
            $this->table(
                ['ID', 'Name', 'Short Name', 'Prefix Code', 'Status'],
                [[$company->id, $company->name, $company->short_name, $company->prefix_code, $company->status ? 'Active' : 'Inactive']]
            );

            return 0;
        } catch (\Exception $e) {
            $this->error("Failed to create partner company: " . $e->getMessage());
            return 1;
        }
    }
}
