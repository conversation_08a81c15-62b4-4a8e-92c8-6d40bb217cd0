<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\PartnerManagement\Models\PMS\PMSProject;
use Modules\PartnerManagement\Models\PMS\PMSProjectPermission;
use Modules\PartnerManagement\Models\PMS\PMSSchoolLevel;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Modules\PartnerManagement\Models\Partner;
use Modules\PartnerManagement\Models\PartnerCompany;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;

class CreatePMSAccountCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pms:create-account
                            {--province= : PMSUnit with level=2 (Sở giáo dục)}
                            {--district= : PMSUnit with level=3 (Phòng giáo dục)}
                            {--ward= : Ward code (PMSWard ID)}
                            {--school_name= : School name}
                            {--username= : Username for login}
                            {--school_level= : School level code (PMSSchoolLevel ID)}
                            {--branches=1 : Number of school branches}
                            {--address= : School address}
                            {--phone= : School phone number}
                            {--email= : School email address}
                            {--projects=* : Project IDs to assign permissions (comma separated)}
                            {--nutrition_module : Enable Nutrition module for project ID 2}
                            {--finance_module : Enable Finance module for project ID 2}
                            {--duration= : Subscription duration in months (1, 3, 6, 12, 24, 36)}
                            {--partner= : Partner ID to get license from}
                            {--license= : Specific PartnerLicense ID to assign (optional)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new PMS account with school unit and project permissions';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get options or prompt for input
        $provinceId = $this->option('province');
        if (empty($provinceId)) {
            // List available PMSUnits with level=2 (Sở giáo dục)
            $provinces = PMSUnit::where('level', 2)->isActive()->orderBy('name')->get();
            if ($provinces->isEmpty()) {
                $this->error('No Sở giáo dục units found in the PMS database.');
                return 1;
            }

            $this->info('Available Sở giáo dục units:');
            $this->table(
                ['ID', 'Name'],
                $provinces->map(function ($province) {
                    return [
                        $province->id,
                        $province->name,
                    ];
                })
            );

            $provinceId = $this->ask('Enter the Sở giáo dục unit ID');
        }

        // Validate province unit
        $province = PMSUnit::where('id', $provinceId)->isActive()->where('level', 2)->first();
        if (!$province) {
            $this->error("Sở giáo dục unit with ID {$provinceId} not found.");
            return 1;
        }

        // Get district
        $districtId = $this->option('district');
        if (empty($districtId)) {
            // List available PMSUnits with level=3 (Phòng giáo dục) for the selected province
            $districts = PMSUnit::where('level', 3)->isActive()->where('province', $province->province)->orderBy('name')->get();
            if ($districts->isEmpty()) {
                $this->error("No Phòng giáo dục units found for Sở giáo dục {$province->name}.");
                return 1;
            }

            $this->info("Available Phòng giáo dục units in {$province->name}:");
            $this->table(
                ['ID', 'Name'],
                $districts->map(function ($district) {
                    return [
                        $district->id,
                        $district->name,
                    ];
                })
            );

            $districtId = $this->ask('Enter the Phòng giáo dục unit ID');
        }

        // Validate district unit
        $district = PMSUnit::where('id', $districtId)
            ->isActive()
            ->where('level', 3)
            ->first();
        if (!$district) {
            $this->error("Phòng giáo dục unit with ID {$districtId} not found.");
            return 1;
        }

        // Get ward
        $wardId = $this->option('ward');
        if (empty($wardId)) {
            // List available wards for the selected district unit
            $wards = PMSWard::where('district', $district->district)->orderBy('name')->get();
            if ($wards->isEmpty()) {
                $this->error("No wards found for Phòng giáo dục {$district->name}.");
                return 1;
            }

            $this->info("Available wards in {$district->name}:");
            $this->table(
                ['ID', 'Name'],
                $wards->map(function ($ward) {
                    return [
                        $ward->id,
                        $ward->name,
                    ];
                })
            );

            $wardId = $this->ask('Enter the ward ID');
        }

        // Validate ward
        $ward = PMSWard::where('id', $wardId)
            ->where('district', $district->district)
            ->first();
        if (!$ward) {
            $this->error("Ward with ID {$wardId} not found in Phòng giáo dục {$district->name}.");
            return 1;
        }

        // Get school name
        $schoolName = $this->option('school_name');
        if (empty($schoolName)) {
            $schoolName = $this->ask('Enter the school name');
        }

        // Get username
        $username = $this->option('username');
        if (empty($username)) {
            $username = $this->ask('Enter the username for login');
        }

        // Check if username already exists
        $existingUser = PMSUser::where('username', $username)->first();
        if ($existingUser) {
            $this->error("Username '{$username}' already exists in the PMS system.");
            return 1;
        }

        // Get school level
        $schoolLevelId = $this->option('school_level');
        if (empty($schoolLevelId)) {
            // List available school levels
            $schoolLevels = PMSSchoolLevel::orderBy('name')->get();
            if ($schoolLevels->isEmpty()) {
                $this->error("No school levels found in the PMS database.");
                return 1;
            }

            $this->info("Available school levels:");
            $this->table(
                ['ID', 'Name'],
                $schoolLevels->map(function ($level) {
                    return [
                        $level->id,
                        $level->name,
                    ];
                })
            );

            $schoolLevelId = $this->ask('Enter the school level ID');
        }

        // Validate school level
        $schoolLevel = PMSSchoolLevel::find($schoolLevelId);
        if (!$schoolLevel) {
            $this->error("School level with ID {$schoolLevelId} not found.");
            return 1;
        }

        // Get number of branches
        $branches = (int) $this->option('branches');
        if ($branches <= 0) {
            $branches = (int) $this->ask('Enter the number of school branches', 1);
        }

        // Get address
        $address = $this->option('address');
        if (empty($address)) {
            $address = $this->ask('Enter the school address');
        }

        // Get phone
        $phone = $this->option('phone');
        if (empty($phone)) {
            $phone = $this->ask('Enter the school phone number');
        }

        // Get email
        $email = $this->option('email');
        if (empty($email)) {
            $email = $this->ask('Enter the school email address');
        }

        // Get projects
        $projectIds = $this->option('projects');
        if (empty($projectIds) || (is_array($projectIds) && count($projectIds) === 0)) {
            // List available projects
            $projects = PMSProject::orderBy('name')->get();
            if ($projects->isEmpty()) {
                $this->error("No projects found in the PMS database.");
                return 1;
            }

            $this->info("Available projects:");
            $this->table(
                ['ID', 'Name'],
                $projects->map(function ($project) {
                    return [
                        $project->id,
                        $project->name ?? 'Unnamed Project',
                    ];
                })
            );

            $projectInput = $this->ask('Enter project IDs (comma separated)');
            $projectIds = array_map('trim', explode(',', $projectInput));
        }

        // Validate projects
        $validProjects = [];
        foreach ($projectIds as $projectId) {
            $project = PMSProject::find($projectId);
            if (!$project) {
                $this->warn("Project with ID {$projectId} not found. Skipping.");
                continue;
            }
            $validProjects[] = $project;
        }

        if (empty($validProjects)) {
            $this->error("No valid projects selected.");
            return 1;
        }

        // Check if project ID 2 is selected and handle module selection
        $nutritionModule = $this->option('nutrition_module');
        $financeModule = $this->option('finance_module');
        $selectedModules = [];

        $hasProject2 = collect($validProjects)->contains(function ($project) {
            return $project->id == 2;
        });

        if ($hasProject2) {
            if (!$nutritionModule && !$financeModule) {
                $this->info("Project ID 2 selected. Please choose additional modules:");
                $this->info("1. Dinh dưỡng");
                $this->info("2. Thu chi");
                $this->info("3. Both");

                $moduleChoice = $this->ask("Enter your choice (1-3)", "3");

                switch ($moduleChoice) {
                    case "1":
                        $nutritionModule = true;
                        break;
                    case "2":
                        $financeModule = true;
                        break;
                    case "3":
                    default:
                        $nutritionModule = true;
                        $financeModule = true;
                        break;
                }
            }

            if ($nutritionModule) {
                $selectedModules[] = 1; // Dinh dưỡng module ID
            }

            if ($financeModule) {
                $selectedModules[] = 2; // Thu chi module ID
            }

            $this->info("Selected modules for Project ID 2:");
            if ($nutritionModule) {
                $this->info("- Dinh dưỡng");
            }
            if ($financeModule) {
                $this->info("- Thu chi");
            }
        }

        // Get subscription duration
        $duration = $this->option('duration');
        $validDurations = [1, 3, 6, 12, 24, 36];

        if (empty($duration) || !in_array((int)$duration, $validDurations)) {
            $this->info("Available subscription durations (months):");
            foreach ($validDurations as $validDuration) {
                $this->info("- {$validDuration}");
            }

            $duration = $this->ask("Enter subscription duration in months", 12);

            if (!in_array((int)$duration, $validDurations)) {
                $this->warn("Invalid duration selected. Using default of 12 months.");
                $duration = 12;
            }
        }

        $this->info("Selected subscription duration: {$duration} months");

        // Get partner if not provided
        $partnerId = $this->option('partner');
        if (empty($partnerId)) {
            // List available partners
            $partners = Partner::with('company')
                ->where('status', true)
                ->whereNotNull('company_id')
                ->get();

            if ($partners->isNotEmpty()) {
                $this->info("Available partners:");
                $this->table(
                    ['ID', 'Name', 'Company', 'Email', 'Phone'],
                    $partners->map(function ($partner) {
                        return [
                            $partner->id,
                            $partner->name,
                            $partner->company ? $partner->company->name : 'N/A',
                            $partner->email,
                            $partner->phone,
                        ];
                    })
                );

                if ($this->confirm("Do you want to select a partner to get a license from?", true)) {
                    $partnerId = $this->ask("Enter the partner ID");

                    // Validate partner
                    $partner = Partner::find($partnerId);
                    if (!$partner) {
                        $this->warn("Partner with ID {$partnerId} not found. Will try to find any available license.");
                        $partnerId = null;
                    } else {
                        $this->info("Selected partner: {$partner->name}");

                        // Check if partner has available licenses
                        $licenseCount = PartnerLicense::where('status', PartnerLicense::STATUS_ASSIGNED)
                            ->where('company_id', $partner->company_id)
                            ->whereNull('project_account')
                            ->count();

                        if ($licenseCount == 0) {
                            $this->warn("Selected partner has no available licenses. Will try to find any available license.");
                        } else {
                            $this->info("Selected partner has {$licenseCount} available licenses.");
                        }
                    }
                }
            } else {
                $this->warn("No active partners found. Will try to find any available license.");
            }
        }

        // Validate input
        $validator = Validator::make([
            'province_unit_id' => $provinceId,
            'district_unit_id' => $districtId,
            'ward_id' => $wardId,
            'school_name' => $schoolName,
            'username' => $username,
            'school_level_id' => $schoolLevelId,
            'branches' => $branches,
            'address' => $address,
            'phone' => $phone,
            'email' => $email,
        ], [
            'province_unit_id' => 'required',
            'district_unit_id' => 'required',
            'ward_id' => 'required',
            'school_name' => 'required|string|max:255',
            'username' => 'required|string|max:100',
            'school_level_id' => 'required',
            'branches' => 'required|integer|min:1',
            'address' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:100',
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        // Create the PMS account
        DB::connection('mysql_pms')->beginTransaction();
        try {
            // Generate a random password
            $password = "123456";

            // Create the unit (school)
            $unit = new PMSUnit();
            $unit->name = $schoolName;
            $unit->province = $province->province;
            $unit->district = $district->district;
            $unit->ward = $wardId;
            $unit->address = $address;
            $unit->phone = $phone;
            $unit->email = $email;
            $unit->school_level = $schoolLevelId;
            $unit->schoollevel = $schoolLevelId;
            $unit->cpu_id = 0;
            $unit->cpu_sync = 1;
            $unit->cpu_level = '01';
            $unit->level = 4; // Cấp trường
            $unit->status = 1;
            $unit->datecreated = date('Y-m-d H:i:s');
            $unit->parent_id = $district->id; // Set parent to the selected Phòng giáo dục
            $unit->school_points = 1;
            $unit->saleaccount = 'partner_management';
            $unit->salemonth = date('m');
            $unit->school_type = 0; // Công lập/tư thục
            $unit->calo_494 = 1;
            $unit->calo_494_from = '2010-01-01';

            $unit->save();

            // Create the user
            $user = new PMSUser();
            $user->username = $username;
            $user->password = md5($password);
            $user->fullname = $schoolName;
            $user->email = $email;
            $user->phone = $phone;
            $user->unit_id = $unit->id;
            $user->must_change_pass = 1;
            $user->school_point = $unit->school_points;
            $user->superadmin = 1;
            $user->status = 1;
            $user->datecreated = date('Y-m-d H:i:s');
            $user->save();

            // Create project permissions
            foreach ($validProjects as $project) {
                $permission = new PMSProjectPermission();
                $permission->unit_id = $unit->id;
                $permission->project_id = $project->id;
                $permission->project_name = $project->name;
                $permission->actived_at = date('Y-m-d H:i:s');
                $permission->deactived_at = date('Y-m-d H:i:s', strtotime("+{$duration} months"));
                $permission->saleaccount = $unit->saleaccount;
                $permission->actived = 1;
                $permission->saletype = 1; // 1 - Chính thức, 0 - demo

                // Set group_modules based on selection for project ID 2
                if ($project->id == 2 && !empty($selectedModules)) {
                    $permission->group_modules = implode(',', $selectedModules);
                } else {
                    $permission->group_modules = '';
                }

                $permission->save();
            }

            // Find and assign a PartnerLicense to the user
            $licenseId = $this->option('license');
            $partnerId = $this->option('partner');
            $assignedLicense = null;

            // If a specific license ID was provided
            if (!empty($licenseId)) {
                // Try to find the specific license
                $license = PartnerLicense::find($licenseId);

                if (!$license) {
                    $this->warn("License with ID {$licenseId} not found. Will try to find an available license.");
                } elseif ($license->status != PartnerLicense::STATUS_ASSIGNED) {
                    $this->warn("License with ID {$licenseId} is not in ASSIGNED status. Will try to find an available license.");
                } else {
                    $assignedLicense = $license;
                }
            }
            // If a partner ID was provided
            elseif (!empty($partnerId)) {
                // Get the partner
                $partner = Partner::find($partnerId);

                if (!$partner) {
                    $this->warn("Partner with ID {$partnerId} not found. Will try to find any available license.");
                } else {
                    $this->info("Looking for an available license from partner: {$partner->name}");

                    // Find an available license from this partner's company
                    $assignedLicense = PartnerLicense::where('status', PartnerLicense::STATUS_ASSIGNED)
                        ->where('company_id', $partner->company_id)
                        ->whereNull('project_account')
                        ->first();

                    if (!$assignedLicense) {
                        $this->warn("No available licenses found for partner {$partner->name}. Will try to find any available license.");
                    }
                }
            }

            // If no specific license or partner license was found, find any available license
            if (!$assignedLicense) {
                $this->info("Looking for any available license with STATUS_ASSIGNED...");
                $assignedLicense = PartnerLicense::where('status', PartnerLicense::STATUS_ASSIGNED)
                    ->whereNotNull('company_id')
                    ->whereNull('project_account')
                    ->first();
            }

            // If a license was found, update it with the user's information and change status
            if ($assignedLicense) {
                $this->info("Found license: {$assignedLicense->code}");

                // Update license information
                $assignedLicense->project_account = $username;
                $assignedLicense->status = PartnerLicense::STATUS_ACTIVATED;
                $assignedLicense->activated_at = now();
                $assignedLicense->expired_at = now()->addMonths($duration);
                $assignedLicense->project_unit_ref = $unit->id;
                $assignedLicense->save();

                $this->info("License {$assignedLicense->code} has been activated for user {$username}");
                $this->info("License expiration date: {$assignedLicense->expired_at->format('Y-m-d H:i:s')}");

                // Send notification to the partner if the license belongs to a partner
                if ($assignedLicense->company_id) {
                    // Find partners associated with this company
                    $partners = Partner::where('company_id', $assignedLicense->company_id)
                        ->where('status', true)
                        ->get();

                    if ($partners->isNotEmpty()) {
                        foreach ($partners as $partner) {
                            // Prepare notification message
                            $message = [
                                'title' => 'Kích hoạt license thành công',
                                'body' => "License {$assignedLicense->code} đã được kích hoạt cho tài khoản {$username}",
                                'type' => 'license_activated',
                                'option' => 'update',
                                'data' => [
                                    'license_id' => $assignedLicense->id,
                                    'license_code' => $assignedLicense->code,
                                    'username' => $username,
                                    'school_name' => $schoolName,
                                    'activated_at' => $assignedLicense->activated_at->format('Y-m-d H:i:s'),
                                    'expired_at' => $assignedLicense->expired_at->format('Y-m-d H:i:s'),
                                    'duration' => $duration
                                ]
                            ];

                            // Broadcast the notification
                            try {
                                event(new PartnerNotificationEvent($partner, $message));
                                $this->info("Notification sent to partner: {$partner->name}");
                            } catch (\Exception $e) {
                                $this->warn("Failed to send notification to partner {$partner->name}: " . $e->getMessage());
                            }
                        }
                    } else {
                        $this->warn("No active partners found for company ID {$assignedLicense->company_id}. No notifications sent.");
                    }
                }
            } else {
                $this->warn("No available licenses found with STATUS_ASSIGNED. The account was created without a license.");
            }

            DB::connection('mysql_pms')->commit();

            $this->info("PMS account created successfully!");

            // Prepare account info with license details if available
            $accountInfo = [
                'School Name' => $schoolName,
                'Username' => $username,
                'Password' => $password,
                'Unit ID' => $unit->id,
            ];

            if ($assignedLicense) {
                $accountInfo['License Code'] = $assignedLicense->code;
                $accountInfo['License Status'] = PartnerLicense::STATUS_LABELS[$assignedLicense->status] ?? 'Unknown';
                $accountInfo['License Company'] = $assignedLicense->company ? $assignedLicense->company->name : 'N/A';
                $accountInfo['Activated At'] = $assignedLicense->activated_at ? $assignedLicense->activated_at->format('Y-m-d H:i:s') : 'N/A';
                $accountInfo['Expires At'] = $assignedLicense->expired_at ? $assignedLicense->expired_at->format('Y-m-d H:i:s') : 'N/A';
            } else {
                $accountInfo['License Code'] = 'No license assigned';
                $accountInfo['License Status'] = 'N/A';
                $accountInfo['License Company'] = 'N/A';
                $accountInfo['Activated At'] = 'N/A';
                $accountInfo['Expires At'] = 'N/A';
            }

            $this->table(
                ['School Name', 'Username', 'Password', 'Unit ID', 'License Code', 'License Status', 'License Company', 'Activated At', 'Expires At'],
                [$accountInfo]
            );

            // Prepare project permissions table with additional information
            $projectTableData = [];
            foreach ($validProjects as $project) {
                $row = [
                    'Project ID' => $project->id,
                    'Project Name' => $project->name ?? 'Unnamed Project',
                    'Duration' => $duration . ' months',
                ];

                // Add module information for project ID 2
                if ($project->id == 2) {
                    $moduleNames = [];
                    if ($nutritionModule) {
                        $moduleNames[] = 'Dinh dưỡng';
                    }
                    if ($financeModule) {
                        $moduleNames[] = 'Thu chi';
                    }
                    $row['Modules'] = !empty($moduleNames) ? implode(', ', $moduleNames) : 'None';
                } else {
                    $row['Modules'] = 'N/A';
                }

                $projectTableData[] = $row;
            }

            $this->info("Project permissions assigned:");
            $this->table(
                ['Project ID', 'Project Name', 'Duration', 'Modules'],
                $projectTableData
            );

            return 0;
        } catch (\Exception $e) {
            DB::connection('mysql_pms')->rollBack();
            $this->error("Failed to create PMS account: " . $e->getMessage());
            return 1;
        }
    }
}
