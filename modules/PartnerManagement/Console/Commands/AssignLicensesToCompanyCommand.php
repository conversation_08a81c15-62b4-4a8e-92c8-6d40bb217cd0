<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Modules\PartnerManagement\Models\PartnerCompany;
use Modules\PartnerManagement\Models\PartnerLicense;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AssignLicensesToCompanyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'partner:assign-licenses {--company_id= : ID of the company to assign licenses to}
                                                   {--count=1 : Number of licenses to assign}
                                                   {--month= : Duration in months for the licenses}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign unactivated licenses with specified duration to a company';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get company ID from option or prompt
        $companyId = $this->option('company_id');
        if (empty($companyId)) {
            // List available companies
            $companies = PartnerCompany::where('status', true)->get();

            if ($companies->isEmpty()) {
                $this->error('No active companies found. Please create a company first.');
                return 1;
            }

            $this->info('Available companies:');
            $this->table(
                ['ID', 'Name', 'Short Name', 'Prefix Code'],
                $companies->map(function ($company) {
                    return [
                        $company->id,
                        $company->name,
                        $company->short_name,
                        $company->prefix_code,
                    ];
                })
            );

            $companyId = $this->ask('Enter the ID of the company to assign licenses to');
        }

        // Validate company exists
        $company = PartnerCompany::find($companyId);
        if (!$company) {
            $this->error("Company with ID {$companyId} not found.");
            return 1;
        }

        // Get count from option or prompt
        $count = (int) $this->option('count');
        if ($count <= 0) {
            $count = (int) $this->ask('How many licenses do you want to assign?', 1);

            if ($count <= 0) {
                $this->error('Count must be a positive integer');
                return 1;
            }
        }

        // Get month from option or prompt
        $month = (int) $this->option('month');
        if ($month <= 0) {
            $month = (int) $this->ask('Enter the duration in months for the licenses:', 1);

            if ($month <= 0) {
                $this->error('Month must be a positive integer');
                return 1;
            }
        }

        // Check if there are enough unassigned licenses with specified month
        $availableLicenses = PartnerLicense::where('status', PartnerLicense::STATUS_CREATED)
            ->whereNull('company_id')
            ->where('month', $month)
            ->count();

        if ($availableLicenses < $count) {
            $this->error("Not enough unassigned licenses with {$month} month duration available. Only {$availableLicenses} licenses available.");

            if ($availableLicenses > 0 && $this->confirm("Do you want to assign all {$availableLicenses} available licenses instead?")) {
                $count = $availableLicenses;
            } else {
                return 1;
            }
        }

        $this->info("Assigning {$count} licenses with {$month} month duration to company: {$company->name}");
        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $assignedCount = 0;

        DB::beginTransaction();
        try {
            // Get unassigned licenses with specified month
            $licenses = PartnerLicense::where('status', PartnerLicense::STATUS_CREATED)
                ->whereNull('company_id')
                ->where('month', $month)
                ->limit($count)
                ->get();

            $now = Carbon::now();

            foreach ($licenses as $license) {
                $license->company_id = $company->id;
                $license->assigned_at = $now;
                $license->status = PartnerLicense::STATUS_ASSIGNED;
                $license->save();

                $assignedCount++;
                $bar->advance();
            }

            DB::commit();
            $bar->finish();
            $this->newLine();
            $this->info("Successfully assigned {$assignedCount} licenses to company: {$company->name}");

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->newLine();
            $this->error("Failed to assign licenses: " . $e->getMessage());
            $this->info("Assigned {$assignedCount} licenses before failure.");

            return 1;
        }
    }
}
