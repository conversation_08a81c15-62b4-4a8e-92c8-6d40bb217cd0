<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Modules\PartnerManagement\Models\Partner;
use App\Models\Product;

class PMSInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pms:message {content}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send test notifications to a partner';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $product = Product::find('100');
        // $pmsProjectRefs = $product->pmsProjectRefs;

        // $this->table(
        //     ['ID', 'PMS Project ID', 'PMS Project Name', 'Status', 'Product ID'],
        //     $pmsProjectRefs
        // );
        // dd('-----------');

        $partner = Partner::where('id', 1)->first();

        if (!$partner) {
            $this->error('Partner with ID 1 not found. Please create a partner first.');
            return 1;
        }

        $this->info('Sending test notifications to partner: ' . $partner->name);

        // Send success notification
        $partner->sendPusherNotification(
            $this->argument('content'),
            '<PERSON><PERSON> hạn thành công cho đơn vị Trường MN Nghĩa Tân',
            'license_activated',
            [
                'license_id' => 1,
                'license_code' => 'ABC123',
                'activated_at' => now()->toDateTimeString(),
                'expired_at' => now()->addYear()->toDateTimeString(),
            ]
        );
        $this->info('Success notification sent.');

        // // Wait 2 seconds before sending the next notification
        // sleep(2);

        // // Send info notification
        // $partner->sendPusherNotification(
        //     'Account Created',
        //     'A new account has been created for your school.',
        //     'account_created',
        //     [
        //         'username' => 'school_user',
        //         'school_name' => 'Example School',
        //     ]
        // );
        // $this->info('Info notification sent.');

        // // Wait 2 seconds before sending the next notification
        // sleep(2);

        // // Send warning notification
        // $partner->sendPusherNotification(
        //     'License Expiring Soon',
        //     'Your license will expire in 30 days. Please renew it soon.',
        //     'license_expired',
        //     [
        //         'license_id' => 1,
        //         'license_code' => 'ABC123',
        //         'days_remaining' => 30,
        //     ]
        // );
        // $this->info('Warning notification sent.');

        // // Wait 2 seconds before sending the next notification
        // sleep(2);

        // // Send default notification
        // $partner->sendPusherNotification(
        //     'System Notification',
        //     'This is a default system notification with no specific type.',
        //     'default',
        //     [
        //         'timestamp' => now()->toDateTimeString(),
        //     ]
        // );
        // $this->info('Default notification sent.');

        // // Wait 2 seconds before sending the next notification
        // sleep(2);

        // // Send error notification
        // $partner->sendPusherNotification(
        //     'Error Occurred',
        //     'An error occurred while processing your request. Please try again later.',
        //     'error',
        //     [
        //         'error_code' => 'ERR-1001',
        //         'timestamp' => now()->toDateTimeString(),
        //     ]
        // );
        // $this->info('Error notification sent.');

        // // Wait 2 seconds before sending the next notification
        // sleep(2);

        // // Send info notification
        // $partner->sendPusherNotification(
        //     'Information',
        //     'Your account has been updated with the latest information.',
        //     'info',
        //     [
        //         'timestamp' => now()->toDateTimeString(),
        //     ]
        // );
        // $this->info('Info notification sent.');

        // $this->info('All test notifications sent successfully!');
        return 0;
    }
}
