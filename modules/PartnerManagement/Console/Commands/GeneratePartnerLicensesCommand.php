<?php

namespace Modules\PartnerManagement\Console\Commands;

use Illuminate\Console\Command;
use Modules\PartnerManagement\Models\PartnerLicense;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class GeneratePartnerLicensesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'partner:generate-licenses {count=1 : Number of licenses to generate} {month=1 : Duration in months for the licenses}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate multiple partner licenses with random unique codes and specified duration in months';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (int) $this->argument('count');
        $month = (int) $this->argument('month');

        if ($count <= 0) {
            $this->error('Count must be a positive integer');
            return 1;
        }

        if ($month <= 0) {
            $this->error('Month must be a positive integer');
            return 1;
        }

        $this->info("Generating {$count} partner licenses with {$month} month duration...");
        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $generatedCount = 0;

        DB::beginTransaction();
        try {
            for ($i = 0; $i < $count; $i++) {
                // Generate a unique random code
                $code = $this->generateUniqueCode();

                // Create the license
                PartnerLicense::create([
                    'code' => $code,
                    'status' => PartnerLicense::STATUS_CREATED,
                    'month' => $month
                ]);

                $generatedCount++;
                $bar->advance();
            }

            DB::commit();
            $bar->finish();
            $this->newLine();
            $this->info("Successfully generated {$generatedCount} partner licenses.");

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->newLine();
            $this->error("Failed to generate licenses: " . $e->getMessage());
            $this->info("Generated {$generatedCount} licenses before failure.");

            return 1;
        }
    }

    /**
     * Generate a unique random code for a license.
     *
     * @return string
     */
    protected function generateUniqueCode()
    {
        $maxAttempts = 10;
        $attempt = 0;

        do {
            // Generate a random uppercase string of 100 characters
            $code = strtoupper(Str::random(20)) . time();

            // Check if the code already exists
            $exists = PartnerLicense::where('code', $code)->exists();

            $attempt++;

            // If we've tried too many times, throw an exception
            if ($attempt >= $maxAttempts && $exists) {
                throw new \Exception("Failed to generate a unique code after {$maxAttempts} attempts.");
            }
        } while ($exists);

        return $code;
    }
}
