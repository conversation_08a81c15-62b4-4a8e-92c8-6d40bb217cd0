<?php

namespace Modules\PartnerManagement\Jobs;

class ClearLicenseCacheJob
{
    private $licenseId;
    private $projectUnitRef;

    public function __construct($licenseId, $projectUnitRef = null)
    {
        $this->licenseId = $licenseId;
        $this->projectUnitRef = $projectUnitRef;
    }

    public function handle()
    {
        \Cache::forget('partner_license_activities_' . $this->licenseId);

        if ($this->projectUnitRef) {
            \Cache::forget('partner_license_unit_' . $this->projectUnitRef);
        }
    }
}
