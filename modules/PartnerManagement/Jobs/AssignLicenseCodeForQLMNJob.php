<?php

namespace Modules\PartnerManagement\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Modules\PartnerManagement\Models\Partner;
use App\Models\Product;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;
use DB;
use Modules\PartnerManagement\Models\PMS\PMSProjectPermission;
use Modules\PartnerManagement\Models\PartnerLicense;
use Carbon\Carbon;

class AssignLicenseCodeForQLMNJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    

    protected $data;
    protected $company_id;
    protected $product_id;
    protected $expired_time;
    protected $pms_unit_id;
    protected $pms_account;
    protected $date_start;
    protected $date_end;

    /**
     * Create a new job instance.
     * @param array $data
     */
    public function __construct($company_id, $product_id, $expired_time, $pms_unit_id, $pms_account, $date_start, $date_end)
    {
        $this->company_id = $company_id;
        $this->product_id = $product_id;
        $this->expired_time = $expired_time;
        $this->pms_unit_id = $pms_unit_id;
        $this->pms_account = $pms_account;
        $this->date_start = $date_start;
        $this->date_end = $date_end;
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {
        //DB::beginTransaction();
        try {

            $license = PartnerLicense::where('status', PartnerLicense::STATUS_ASSIGNED)
                ->where('company_id', $this->company_id)
                ->whereNull('project_unit_ref')
                ->where('month', intval($this->expired_time))
                ->first();

            if ($license == null) throw new \Exception("Không có giấy phép thời gian sử dụng " . $this->expired_time . " tháng");

            if ($license) {
                $license->project_code = $this->product_id;
                $license->assigned_at = now();
                $license->project_unit_ref = $this->pms_unit_id;
                $license->project_account = vietec_sanitize_username($this->pms_account);
                // ...
                $license->activated_at = Carbon::parse($this->date_start)->startOfDay();
                //$permission->actived_at = date('Y-m-d H:i:s');
                $license->expired_at = Carbon::parse($this->date_start)->startOfDay()->addMonths(intval($this->expired_time));
                $license->status = PartnerLicense::STATUS_ACTIVATED;
                $license->save();
            }
            //DB::commit();

            return $license;
        } catch (\Exception $e) {
            //DB::rollBack();
            throw $e;
        }
    }
}
