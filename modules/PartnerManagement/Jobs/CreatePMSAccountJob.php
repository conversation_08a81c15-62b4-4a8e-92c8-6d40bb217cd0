<?php

namespace Modules\PartnerManagement\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Modules\PartnerManagement\Models\Partner;
use App\Models\Product;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;
use DB;
use Modules\PartnerManagement\Models\PMS\PMSProjectPermission;

class CreatePMSAccountJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;

    /**
     * Create a new job instance.
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {
        //return $this->data;
        //DB::connection('mysql_pms')->beginTransaction();
        try {
            // Generate a random password
            $password = "qlmn@123456";

            $district = PMSUnit::where('id', $this->data['district_id'])->phongGiaoDuc()->isActive()->first();
            $product = Product::find($this->data['product_id']);

            // Create the unit (school)
            $unit = new PMSUnit();
            $unit->name = $this->data['school_name'];
            $unit->province = $district->province;
            $unit->district = $district->district;
            $unit->ward = $this->data['ward_id'];
            $unit->address = $this->data['address'];
            $unit->phone = $this->data['phone'];
            $unit->email = $this->data['email'];
            $unit->school_level = $this->data['school_level'];
            $unit->schoollevel = $this->data['school_level'];
            $unit->cpu_id = 0;
            $unit->cpu_sync = 1;
            $unit->cpu_level = '01';
            $unit->level = 4; // Cấp trường
            $unit->status = 1;
            $unit->datecreated = date('Y-m-d H:i:s');
            $unit->parent_id = $district->id; // Set parent to the selected Phòng giáo dục
            $unit->school_points = $this->data['school_point'];
            $unit->saleaccount = 'partner_management';
            $unit->salemonth = intval($this->data['expired_time']);
            $unit->school_type = 0; // Công lập/tư thục
            $unit->saletype = intval($this->data['expired_time'])== 1 ? 0 : 1; // 1 - Chính thức, 0 - demo
            $unit->calo_494 = 1;
            $unit->calo_494_from = '2010-01-01';

            $unit->save();

            // Create the user
            $user = new PMSUser();
            $user->username = vietec_sanitize_username($this->data['account']);
            $user->password = sha1($password);
            $user->fullname = $this->data['school_name'];
            $user->email = $this->data['email'];
            $user->phone = $this->data['phone'];
            $user->unit_id = $unit->id;
            $user->must_change_pass = 0;
            $user->school_point = $unit->school_points;
            $user->superadmin = 1;
            $user->status = 1;
            $user->datecreated = date('Y-m-d H:i:s');
            $user->save();

            if (isset($product->pmsProjectRefs) && is_iterable($product->pmsProjectRefs)) {
                foreach ($product->pmsProjectRefs as $projectRef) {
                    $permission = new PMSProjectPermission();
                    $permission->unit_id = $unit->id;
                    $permission->project_id = $projectRef->id;
                    $permission->project_name = $projectRef->name;
                    $permission->actived_at = date('Y-m-d H:i:s');
                    $permission->deactived_at = date('Y-m-d H:i:s', strtotime("+{$this->data['expired_time']} months"));
                    $permission->saleaccount = $unit->saleaccount;
                    $permission->salemonth = intval($unit->salemonth);
                    $permission->actived = 1;
                    $permission->saletype = 1; // 1 - Chính thức, 0 - demo

                    // Set group_modules based on selection for project ID 2
                    if ($projectRef->id == 2) {
                        $permission->group_modules = $projectRef->nutrition_boarding;
                    } else {
                        $permission->group_modules = '';
                    }

                    $permission->save();
                }
            }

            //DB::connection('mysql_pms')->commit();

            return [
                'unit' => $unit,
                'user' => $user
            ];
        } catch (\Exception $e) {
            //DB::connection('mysql_pms')->rollBack();
            throw $e;
        }
    }
}
