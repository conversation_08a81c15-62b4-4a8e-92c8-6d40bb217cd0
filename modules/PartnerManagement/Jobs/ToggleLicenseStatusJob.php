<?php

namespace Modules\PartnerManagement\Jobs;

use Exception;
use Illuminate\Support\Facades\DB;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Models\PMS\PMSUnit;

class ToggleLicenseStatusJob
{
    private $licenseId;
    private $action; // 'lock' hoặc 'unlock'
    private $companyId;

    public function __construct($licenseId, $action, $companyId)
    {
        $this->licenseId = $licenseId;
        $this->action = $action;
        $this->companyId = $companyId;
    }

    public function handle()
    {
        $db1 = DB::connection(); // default connection
        $db2 = DB::connection('mysql_pms'); // PMS connection

        try {
            $db1->beginTransaction();
            $db2->beginTransaction();

            // Xác định status hiện tại và status mới dựa trên action
            if ($this->action === 'lock') {
                $currentStatus = PartnerLicense::STATUS_ACTIVATED;
                $newStatus = PartnerLicense::STATUS_PENDING;
                $pmsUnitStatus = 0; // deactivate
                $message = 'Khóa giấy phép thành công!';
                $errorMessage = 'Không thể khóa giấy phép đã hết hạn!';
            } elseif ($this->action === 'unlock') {
                $currentStatus = PartnerLicense::STATUS_PENDING;
                $newStatus = PartnerLicense::STATUS_ACTIVATED;
                $pmsUnitStatus = 1; // active
                $message = 'Mở khóa giấy phép thành công!';
                $errorMessage = 'Không thể mở khóa giấy phép đã hết hạn!';
            } else {
                throw new Exception('Action không hợp lệ. Chỉ chấp nhận "lock" hoặc "unlock".');
            }

            // Tìm license với status hiện tại
            $license = PartnerLicense::where('company_id', $this->companyId)
                ->where('status', $currentStatus)
                ->findOrFail($this->licenseId);

            // Kiểm tra xem license có còn hạn không
            if ($license->expired_at && $license->expired_at->isPast()) {
                throw new Exception($errorMessage);
            }

            // Cập nhật status của PartnerLicense
            $license->status = $newStatus;
            $license->save();

            // Cập nhật status của PMSUnit tương ứng
            if ($license->project_unit_ref) {
                $pmsUnit = PMSUnit::find($license->project_unit_ref);
                if ($pmsUnit) {
                    $pmsUnit->status = $pmsUnitStatus;
                    $pmsUnit->save();
                }
            }

            // Clear cache
            $this->clearLicenseCache($this->licenseId, $license->project_unit_ref);

            $db1->commit();
            $db2->commit();

            return [
                'success' => true,
                'message' => $message,
                'license' => $license
            ];

        } catch (Exception $e) {
            $db1->rollBack();
            $db2->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'license' => null
            ];
        }
    }

    /**
     * Clear cache cho license
     */
    private function clearLicenseCache($licenseId, $projectUnitRef = null)
    {
        \Cache::forget('partner_license_activities_' . $licenseId);

        if ($projectUnitRef) {
            \Cache::forget('partner_license_unit_' . $projectUnitRef);
        }
    }
}
