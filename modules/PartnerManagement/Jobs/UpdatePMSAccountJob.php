<?php

namespace Modules\PartnerManagement\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Modules\PartnerManagement\Models\Partner;
use App\Models\Product;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;
use DB;
use Modules\PartnerManagement\Models\PMS\PMSProjectPermission;
use Carbon\Carbon;

class UpdatePMSAccountJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;

    /**
     * Create a new job instance.
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {

        try {

            $this->data['after_username'] = vietec_sanitize_username($this->data['after_username']);
            $unit = PMSUnit::where('id', $this->data['unit_id'])->truongHoc()->isActive()->first();

            if ($unit == null) throw new \Exception("Không tìm thấy đơn vị mã #{$this->data['unit_id']}");

            $user = PMSUser::where('unit_id', $unit->id)->where('username', $this->data['before_username'])->first();

            if ($user == null) throw new \Exception("Đơn vị mã #{$this->data['unit_id']},Không tìm thấy tài khoản {$this->data['before_username']}");

            $user->username = vietec_sanitize_username($this->data['after_username']);
            $user->save();

            //dd($this->data);
            // $project_permissions = PMSProjectPermission::where('unit_id', $unit->id)
            //     ->where('actived', 1)
            //     ->get();

            // foreach ($project_permissions as $permission) {
                
            // }
            PMSProjectPermission::where('unit_id', $this->data['unit_id'])
                    ->update([
                        'actived_at' => $this->data['activated_at'],
                        'deactived_at' => $this->data['expired_at']
                    ]);

            return [
                'unit' => $unit,
                'user' => $user
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
